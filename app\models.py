import os
from typing import List, Dict, Optional
from datetime import datetime, timezone, timedelta

from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey, Float, create_engine
from sqlalchemy.orm import declarative_base, relationship, sessionmaker

# Create SQLAlchemy Base
Base = declarative_base()

class EmailLog(Base):
    """Model for storing email processing logs"""

    __tablename__ = 'email_logs'

    id = Column(Integer, primary_key=True)
    message_id = Column(String(255), unique=True, nullable=False)
    sender = Column(String(255), nullable=False)
    recipient = Column(String(255), nullable=False)
    subject = Column(String(255), nullable=True)
    received_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    processed_at = Column(DateTime, nullable=True)
    summary = Column(Text, nullable=True)
    extracted_data = Column(Text, nullable=True)
    whatsapp_summary = Column(Text, nullable=True)
    auto_reply_text = Column(Text, nullable=True)
    status = Column(String(50), default='received')  # received, processed, failed
    error_message = Column(Text, nullable=True)

    # Relationships
    notifications = relationship("WhatsAppNotification", back_populates="email_log")
    replies = relationship("EmailReply", back_populates="email_log")
    voice_calls = relationship("VoiceCall", back_populates="email_log")
    action_monitors = relationship("ActionMonitor", back_populates="email_log")

    def to_dict(self):
        """Convert model to dictionary for API responses"""
        return {
            'id': self.id,
            'message_id': self.message_id,
            'sender': self.sender,
            'recipient': self.recipient,
            'subject': self.subject,
            'received_at': self.received_at.isoformat() if self.received_at else None,
            'processed_at': self.processed_at.isoformat() if self.processed_at else None,
            'summary': self.summary,
            'extracted_data': self.extracted_data,
            'whatsapp_summary': self.whatsapp_summary,
            'auto_reply_text': self.auto_reply_text,
            'status': self.status,
            'error_message': self.error_message
        }


class WhatsAppNotification(Base):
    """Model for storing WhatsApp notification logs"""

    __tablename__ = 'whatsapp_notifications'

    id = Column(Integer, primary_key=True)
    email_log_id = Column(Integer, ForeignKey('email_logs.id'), nullable=False)
    recipient = Column(String(50), nullable=False)  # Phone number
    message = Column(Text, nullable=False)
    sent_at = Column(DateTime, nullable=True)
    status = Column(String(50), default='pending')  # pending, sent, delivered, failed
    error_message = Column(Text, nullable=True)
    retry_count = Column(Integer, default=0)

    # Relationship
    email_log = relationship("EmailLog", back_populates="notifications")

    def to_dict(self):
        """Convert model to dictionary for API responses"""
        return {
            'id': self.id,
            'email_log_id': self.email_log_id,
            'recipient': self.recipient,
            'message': self.message,
            'sent_at': self.sent_at.isoformat() if self.sent_at else None,
            'status': self.status,
            'error_message': self.error_message,
            'retry_count': self.retry_count
        }


class EmailReply(Base):
    """Model for storing email reply logs"""

    __tablename__ = 'email_replies'

    id = Column(Integer, primary_key=True)
    email_log_id = Column(Integer, ForeignKey('email_logs.id'), nullable=False)
    reply_to = Column(String(255), nullable=False)
    subject = Column(String(255), nullable=True)
    body = Column(Text, nullable=False)
    sent_at = Column(DateTime, nullable=True)
    status = Column(String(50), default='pending')  # pending, sent, failed
    error_message = Column(Text, nullable=True)

    # Relationship
    email_log = relationship("EmailLog", back_populates="replies")

    def to_dict(self):
        """Convert model to dictionary for API responses"""
        return {
            'id': self.id,
            'email_log_id': self.email_log_id,
            'reply_to': self.reply_to,
            'subject': self.subject,
            'body': self.body,
            'sent_at': self.sent_at.isoformat() if self.sent_at else None,
            'status': self.status,
            'error_message': self.error_message
        }


class VoiceCall(Base):
    """Model for storing voice call logs via VAPI"""

    __tablename__ = 'voice_calls'

    id = Column(Integer, primary_key=True)
    email_log_id = Column(Integer, ForeignKey('email_logs.id'), nullable=False)
    recipient = Column(String(50), nullable=False)  # Phone number
    employee_name = Column(String(255), nullable=True)  # Employee name for personalization
    vapi_call_id = Column(String(255), nullable=True)  # VAPI call ID
    status = Column(String(50), default='pending')  # pending, initiated, ringing, answered, completed, failed, no_answer
    initiated_at = Column(DateTime, nullable=True)
    answered_at = Column(DateTime, nullable=True)
    ended_at = Column(DateTime, nullable=True)
    duration = Column(Integer, nullable=True)  # Call duration in seconds
    cost = Column(Float, nullable=True)  # Call cost
    ended_reason = Column(String(100), nullable=True)  # Reason call ended
    error_message = Column(Text, nullable=True)
    retry_count = Column(Integer, default=0)
    next_retry_at = Column(DateTime, nullable=True)  # When to retry if call failed

    # Relationship
    email_log = relationship("EmailLog", back_populates="voice_calls")

    def to_dict(self):
        """Convert model to dictionary for API responses"""
        return {
            'id': self.id,
            'email_log_id': self.email_log_id,
            'recipient': self.recipient,
            'employee_name': self.employee_name,
            'vapi_call_id': self.vapi_call_id,
            'status': self.status,
            'initiated_at': self.initiated_at.isoformat() if self.initiated_at else None,
            'answered_at': self.answered_at.isoformat() if self.answered_at else None,
            'ended_at': self.ended_at.isoformat() if self.ended_at else None,
            'duration': self.duration,
            'cost': self.cost,
            'ended_reason': self.ended_reason,
            'error_message': self.error_message,
            'retry_count': self.retry_count,
            'next_retry_at': self.next_retry_at.isoformat() if self.next_retry_at else None
        }


class ActionMonitor(Base):
    """Model for monitoring all system actions and events"""

    __tablename__ = 'action_monitors'

    id = Column(Integer, primary_key=True)
    email_log_id = Column(Integer, ForeignKey('email_logs.id'), nullable=True)  # Optional link to email
    action_type = Column(String(50), nullable=False)  # email_received, whatsapp_sent, voice_call, email_reply, system_event
    action_category = Column(String(50), nullable=False)  # notification, communication, processing, system
    action_description = Column(Text, nullable=False)  # Human-readable description
    target_recipient = Column(String(255), nullable=True)  # Phone/email of recipient
    employee_name = Column(String(255), nullable=True)  # Employee name if applicable
    status = Column(String(50), nullable=False)  # success, failed, pending, in_progress

    # Timing information
    initiated_at = Column(DateTime, nullable=False, default=lambda: datetime.now(timezone.utc))
    completed_at = Column(DateTime, nullable=True)
    duration_seconds = Column(Integer, nullable=True)  # How long the action took

    # Result information
    success = Column(String(10), nullable=False, default='pending')  # true, false, pending
    error_message = Column(Text, nullable=True)
    response_data = Column(Text, nullable=True)  # JSON response from APIs

    # Cost and metrics
    cost = Column(Float, nullable=True)  # Cost of the action (for calls, SMS, etc.)
    retry_count = Column(Integer, default=0)
    next_retry_at = Column(DateTime, nullable=True)

    # Metadata
    user_agent = Column(String(255), nullable=True)  # For web actions
    ip_address = Column(String(45), nullable=True)  # For web actions
    additional_data = Column(Text, nullable=True)  # JSON for extra information

    # Relationships
    email_log = relationship("EmailLog", back_populates="action_monitors")

    def to_dict(self):
        """Convert model to dictionary for API responses"""
        return {
            'id': self.id,
            'email_log_id': self.email_log_id,
            'action_type': self.action_type,
            'action_category': self.action_category,
            'action_description': self.action_description,
            'target_recipient': self.target_recipient,
            'employee_name': self.employee_name,
            'status': self.status,
            'initiated_at': self.initiated_at.isoformat() if self.initiated_at else None,
            'completed_at': self.completed_at.isoformat() if self.completed_at else None,
            'duration_seconds': self.duration_seconds,
            'success': self.success,
            'error_message': self.error_message,
            'response_data': self.response_data,
            'cost': self.cost,
            'retry_count': self.retry_count,
            'next_retry_at': self.next_retry_at.isoformat() if self.next_retry_at else None,
            'user_agent': self.user_agent,
            'ip_address': self.ip_address,
            'additional_data': self.additional_data
        }

    def mark_completed(self, success: bool, error_message: str = None, response_data: str = None, cost: float = None):
        """Mark the action as completed"""
        self.completed_at = datetime.now(timezone.utc)
        self.success = 'true' if success else 'false'
        self.status = 'success' if success else 'failed'

        if self.initiated_at:
            duration = self.completed_at - self.initiated_at
            self.duration_seconds = int(duration.total_seconds())

        if error_message:
            self.error_message = error_message
        if response_data:
            self.response_data = response_data
        if cost is not None:
            self.cost = cost

    def schedule_retry(self, delay_minutes: int = 10):
        """Schedule this action for retry"""
        self.retry_count += 1
        self.next_retry_at = datetime.now(timezone.utc) + timedelta(minutes=delay_minutes)
        self.status = 'pending_retry'


# Database setup function
def get_db():
    """
    Create database engine and session for PostgreSQL
    """
    SQLALCHEMY_DATABASE_URL = os.getenv(
        "DATABASE_URL",
        "postgresql://username:password@localhost:5432/email_monitor"
    )

    # PostgreSQL engine configuration
    engine = create_engine(
        SQLALCHEMY_DATABASE_URL,
        pool_size=10,
        max_overflow=20,
        pool_pre_ping=True,
        pool_recycle=300
    )

    # Create tables
    Base.metadata.create_all(bind=engine)

    # Create session
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    return SessionLocal()
