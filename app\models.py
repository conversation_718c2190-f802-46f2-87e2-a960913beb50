import os
from typing import List, Dict, Optional
from datetime import datetime, timezone

from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey, Float, create_engine
from sqlalchemy.orm import declarative_base, relationship, sessionmaker

# Create SQLAlchemy Base
Base = declarative_base()

class EmailLog(Base):
    """Model for storing email processing logs"""

    __tablename__ = 'email_logs'

    id = Column(Integer, primary_key=True)
    message_id = Column(String(255), unique=True, nullable=False)
    sender = Column(String(255), nullable=False)
    recipient = Column(String(255), nullable=False)
    subject = Column(String(255), nullable=True)
    received_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    processed_at = Column(DateTime, nullable=True)
    summary = Column(Text, nullable=True)
    extracted_data = Column(Text, nullable=True)
    whatsapp_summary = Column(Text, nullable=True)
    auto_reply_text = Column(Text, nullable=True)
    status = Column(String(50), default='received')  # received, processed, failed
    error_message = Column(Text, nullable=True)

    # Relationships
    notifications = relationship("WhatsAppNotification", back_populates="email_log")
    replies = relationship("EmailReply", back_populates="email_log")
    voice_calls = relationship("VoiceCall", back_populates="email_log")

    def to_dict(self):
        """Convert model to dictionary for API responses"""
        return {
            'id': self.id,
            'message_id': self.message_id,
            'sender': self.sender,
            'recipient': self.recipient,
            'subject': self.subject,
            'received_at': self.received_at.isoformat() if self.received_at else None,
            'processed_at': self.processed_at.isoformat() if self.processed_at else None,
            'summary': self.summary,
            'extracted_data': self.extracted_data,
            'whatsapp_summary': self.whatsapp_summary,
            'auto_reply_text': self.auto_reply_text,
            'status': self.status,
            'error_message': self.error_message
        }


class WhatsAppNotification(Base):
    """Model for storing WhatsApp notification logs"""

    __tablename__ = 'whatsapp_notifications'

    id = Column(Integer, primary_key=True)
    email_log_id = Column(Integer, ForeignKey('email_logs.id'), nullable=False)
    recipient = Column(String(50), nullable=False)  # Phone number
    message = Column(Text, nullable=False)
    sent_at = Column(DateTime, nullable=True)
    status = Column(String(50), default='pending')  # pending, sent, delivered, failed
    error_message = Column(Text, nullable=True)
    retry_count = Column(Integer, default=0)

    # Relationship
    email_log = relationship("EmailLog", back_populates="notifications")

    def to_dict(self):
        """Convert model to dictionary for API responses"""
        return {
            'id': self.id,
            'email_log_id': self.email_log_id,
            'recipient': self.recipient,
            'message': self.message,
            'sent_at': self.sent_at.isoformat() if self.sent_at else None,
            'status': self.status,
            'error_message': self.error_message,
            'retry_count': self.retry_count
        }


class EmailReply(Base):
    """Model for storing email reply logs"""

    __tablename__ = 'email_replies'

    id = Column(Integer, primary_key=True)
    email_log_id = Column(Integer, ForeignKey('email_logs.id'), nullable=False)
    reply_to = Column(String(255), nullable=False)
    subject = Column(String(255), nullable=True)
    body = Column(Text, nullable=False)
    sent_at = Column(DateTime, nullable=True)
    status = Column(String(50), default='pending')  # pending, sent, failed
    error_message = Column(Text, nullable=True)

    # Relationship
    email_log = relationship("EmailLog", back_populates="replies")

    def to_dict(self):
        """Convert model to dictionary for API responses"""
        return {
            'id': self.id,
            'email_log_id': self.email_log_id,
            'reply_to': self.reply_to,
            'subject': self.subject,
            'body': self.body,
            'sent_at': self.sent_at.isoformat() if self.sent_at else None,
            'status': self.status,
            'error_message': self.error_message
        }


class VoiceCall(Base):
    """Model for storing voice call logs via VAPI"""

    __tablename__ = 'voice_calls'

    id = Column(Integer, primary_key=True)
    email_log_id = Column(Integer, ForeignKey('email_logs.id'), nullable=False)
    recipient = Column(String(50), nullable=False)  # Phone number
    employee_name = Column(String(255), nullable=True)  # Employee name for personalization
    vapi_call_id = Column(String(255), nullable=True)  # VAPI call ID
    status = Column(String(50), default='pending')  # pending, initiated, ringing, answered, completed, failed, no_answer
    initiated_at = Column(DateTime, nullable=True)
    answered_at = Column(DateTime, nullable=True)
    ended_at = Column(DateTime, nullable=True)
    duration = Column(Integer, nullable=True)  # Call duration in seconds
    cost = Column(Float, nullable=True)  # Call cost
    ended_reason = Column(String(100), nullable=True)  # Reason call ended
    error_message = Column(Text, nullable=True)
    retry_count = Column(Integer, default=0)
    next_retry_at = Column(DateTime, nullable=True)  # When to retry if call failed

    # Relationship
    email_log = relationship("EmailLog", back_populates="voice_calls")

    def to_dict(self):
        """Convert model to dictionary for API responses"""
        return {
            'id': self.id,
            'email_log_id': self.email_log_id,
            'recipient': self.recipient,
            'employee_name': self.employee_name,
            'vapi_call_id': self.vapi_call_id,
            'status': self.status,
            'initiated_at': self.initiated_at.isoformat() if self.initiated_at else None,
            'answered_at': self.answered_at.isoformat() if self.answered_at else None,
            'ended_at': self.ended_at.isoformat() if self.ended_at else None,
            'duration': self.duration,
            'cost': self.cost,
            'ended_reason': self.ended_reason,
            'error_message': self.error_message,
            'retry_count': self.retry_count,
            'next_retry_at': self.next_retry_at.isoformat() if self.next_retry_at else None
        }


# Database setup function
def get_db():
    """
    Create database engine and session for PostgreSQL
    """
    SQLALCHEMY_DATABASE_URL = os.getenv(
        "DATABASE_URL",
        "postgresql://username:password@localhost:5432/email_monitor"
    )

    # PostgreSQL engine configuration
    engine = create_engine(
        SQLALCHEMY_DATABASE_URL,
        pool_size=10,
        max_overflow=20,
        pool_pre_ping=True,
        pool_recycle=300
    )

    # Create tables
    Base.metadata.create_all(bind=engine)

    # Create session
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    return SessionLocal()
