#!/usr/bin/env python3
"""
Database Table Checker
Checks if all required tables have been created in the database.
"""

import os
import sys
from sqlalchemy import inspect, text, create_engine
from sqlalchemy.orm import sessionmaker

def check_database():
    """Check database tables and schema"""
    try:
        # Get database URL from environment or use default
        database_url = os.getenv(
            "DATABASE_URL",
            "postgresql://username:password@localhost:5432/email_monitor"
        )
        
        print("=== DATABASE CONNECTION CHECK ===")
        print(f"Database URL: {database_url.replace(database_url.split('@')[0].split('://')[-1], '***')}")
        
        # Create engine
        engine = create_engine(database_url)
        
        # Test connection
        with engine.connect() as conn:
            result = conn.execute(text('SELECT 1'))
            print("✅ Database connection: SUCCESS")
        
        # Get inspector
        inspector = inspect(engine)
        tables = inspector.get_table_names()
        
        print(f"\n=== FOUND {len(tables)} TABLES ===")
        for i, table in enumerate(sorted(tables), 1):
            print(f"{i:2d}. {table}")
        
        # Check expected tables
        expected_tables = [
            'email_logs',
            'whatsapp_notifications',
            'email_replies', 
            'voice_calls',
            'voice_call_logs',
            'action_monitors',
            'system_configurations'
        ]
        
        print(f"\n=== TABLE STATUS CHECK ===")
        missing_tables = []
        for table in expected_tables:
            if table in tables:
                print(f"✅ {table}")
            else:
                print(f"❌ {table} (MISSING)")
                missing_tables.append(table)
        
        # Show detailed schema for new tables
        new_tables = ['voice_call_logs', 'action_monitors', 'system_configurations']
        
        for table in new_tables:
            if table in tables:
                print(f"\n=== {table.upper()} SCHEMA ===")
                columns = inspector.get_columns(table)
                for col in columns:
                    nullable = "NULL" if col['nullable'] else "NOT NULL"
                    default = f" DEFAULT {col['default']}" if col.get('default') else ""
                    print(f"  {col['name']:<25} {str(col['type']):<20} {nullable}{default}")
            else:
                print(f"\n=== {table.upper()} ===")
                print("❌ Table not found - needs to be created")
        
        # Summary
        print(f"\n=== SUMMARY ===")
        print(f"Total tables found: {len(tables)}")
        print(f"Expected tables: {len(expected_tables)}")
        print(f"Missing tables: {len(missing_tables)}")
        
        if missing_tables:
            print(f"\n❌ Missing tables: {', '.join(missing_tables)}")
            print("💡 Run the application to auto-create missing tables")
        else:
            print("\n✅ All expected tables are present!")
        
        return len(missing_tables) == 0
        
    except Exception as e:
        print(f"❌ Error checking database: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = check_database()
    sys.exit(0 if success else 1)
