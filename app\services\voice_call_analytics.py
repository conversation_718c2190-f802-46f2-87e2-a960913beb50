"""
Voice Call Analytics Service
Advanced analytics and management for voice call logs.
"""

import json
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timezone, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_
from ..models import VoiceCallLog, EmailLog

logger = logging.getLogger(__name__)


class VoiceCallAnalyticsService:
    """
    Service for managing and analyzing voice call logs.
    """
    
    def __init__(self, db_session: Session):
        """
        Initialize the voice call analytics service.
        
        Args:
            db_session: Database session
        """
        self.db_session = db_session
    
    def create_call_log(self, 
                       email_log_id: Optional[int],
                       employee_id: Optional[int],
                       employee_name: str,
                       employee_phone: str,
                       call_purpose: str = 'email_notification',
                       vapi_assistant_id: Optional[str] = None,
                       message_template: Optional[str] = None,
                       **kwargs) -> VoiceCallLog:
        """
        Create a new voice call log entry.
        
        Args:
            email_log_id: Associated email log ID
            employee_id: Employee ID
            employee_name: Employee name
            employee_phone: Employee phone number
            call_purpose: Purpose of the call
            vapi_assistant_id: VAPI assistant ID
            message_template: Message template used
            **kwargs: Additional call parameters
            
        Returns:
            VoiceCallLog: Created call log entry
        """
        try:
            call_log = VoiceCallLog(
                email_log_id=email_log_id,
                employee_id=employee_id,
                employee_name=employee_name,
                employee_phone=employee_phone,
                call_purpose=call_purpose,
                vapi_assistant_id=vapi_assistant_id,
                message_template=message_template,
                **kwargs
            )
            
            self.db_session.add(call_log)
            self.db_session.commit()
            
            logger.info(f"Created voice call log for {employee_name} ({employee_phone})")
            return call_log
            
        except Exception as e:
            logger.error(f"Failed to create voice call log: {str(e)}")
            self.db_session.rollback()
            raise
    
    def update_call_status(self, 
                          call_id: int,
                          status: str,
                          vapi_call_id: Optional[str] = None,
                          **kwargs) -> bool:
        """
        Update call status and related information.
        
        Args:
            call_id: Call log ID
            status: New status
            vapi_call_id: VAPI call ID
            **kwargs: Additional fields to update
            
        Returns:
            bool: True if successful
        """
        try:
            call_log = self.db_session.query(VoiceCallLog).filter(
                VoiceCallLog.id == call_id
            ).first()
            
            if not call_log:
                logger.error(f"Voice call log {call_id} not found")
                return False
            
            call_log.status = status
            if vapi_call_id:
                call_log.vapi_call_id = vapi_call_id
            
            # Update additional fields
            for key, value in kwargs.items():
                if hasattr(call_log, key):
                    setattr(call_log, key, value)
            
            self.db_session.commit()
            logger.info(f"Updated voice call log {call_id} status to {status}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to update voice call log {call_id}: {str(e)}")
            self.db_session.rollback()
            return False
    
    def mark_call_answered(self, call_id: int, quality_data: Optional[Dict] = None) -> bool:
        """
        Mark a call as answered and update quality metrics.
        
        Args:
            call_id: Call log ID
            quality_data: Quality metrics from VAPI
            
        Returns:
            bool: True if successful
        """
        try:
            call_log = self.db_session.query(VoiceCallLog).filter(
                VoiceCallLog.id == call_id
            ).first()
            
            if not call_log:
                return False
            
            call_log.mark_answered()
            
            if quality_data:
                call_log.call_quality_score = quality_data.get('quality_score')
                call_log.audio_quality = quality_data.get('audio_quality')
                call_log.connection_quality = quality_data.get('connection_quality')
                call_log.background_noise_level = quality_data.get('noise_level')
                call_log.human_detected = 'true' if quality_data.get('human_detected') else 'false'
            
            self.db_session.commit()
            return True
            
        except Exception as e:
            logger.error(f"Failed to mark call {call_id} as answered: {str(e)}")
            self.db_session.rollback()
            return False
    
    def complete_call(self, 
                     call_id: int,
                     success: bool = True,
                     cost_data: Optional[Dict] = None,
                     transcript: Optional[str] = None,
                     error_message: Optional[str] = None) -> bool:
        """
        Mark a call as completed with final metrics.
        
        Args:
            call_id: Call log ID
            success: Whether call was successful
            cost_data: Cost and billing information
            transcript: Call transcript
            error_message: Error message if failed
            
        Returns:
            bool: True if successful
        """
        try:
            call_log = self.db_session.query(VoiceCallLog).filter(
                VoiceCallLog.id == call_id
            ).first()
            
            if not call_log:
                return False
            
            # Calculate cost if provided
            cost = None
            if cost_data:
                call_log.cost_per_minute = cost_data.get('cost_per_minute')
                call_log.billing_increment = cost_data.get('billing_increment', 60)
                cost = call_log.calculate_cost()
            
            call_log.mark_completed(success=success, error_message=error_message, cost=cost)
            
            if transcript:
                call_log.call_transcript = transcript
            
            self.db_session.commit()
            return True
            
        except Exception as e:
            logger.error(f"Failed to complete call {call_id}: {str(e)}")
            self.db_session.rollback()
            return False
    
    def get_call_analytics(self, hours: int = 24) -> Dict:
        """
        Get comprehensive call analytics for the specified period.
        
        Args:
            hours: Time period in hours
            
        Returns:
            Dict: Analytics data
        """
        try:
            cutoff_time = datetime.now(timezone.utc) - timedelta(hours=hours)
            
            # Basic metrics
            total_calls = self.db_session.query(VoiceCallLog).filter(
                VoiceCallLog.initiated_at >= cutoff_time
            ).count()
            
            answered_calls = self.db_session.query(VoiceCallLog).filter(
                VoiceCallLog.initiated_at >= cutoff_time,
                VoiceCallLog.call_answered == 'true'
            ).count()
            
            completed_calls = self.db_session.query(VoiceCallLog).filter(
                VoiceCallLog.initiated_at >= cutoff_time,
                VoiceCallLog.status == 'completed'
            ).count()
            
            # Cost metrics
            total_cost = self.db_session.query(
                func.sum(VoiceCallLog.total_cost)
            ).filter(
                VoiceCallLog.initiated_at >= cutoff_time,
                VoiceCallLog.total_cost.isnot(None)
            ).scalar() or 0.0
            
            # Duration metrics
            avg_duration = self.db_session.query(
                func.avg(VoiceCallLog.duration_seconds)
            ).filter(
                VoiceCallLog.initiated_at >= cutoff_time,
                VoiceCallLog.duration_seconds.isnot(None)
            ).scalar() or 0.0
            
            avg_talk_time = self.db_session.query(
                func.avg(VoiceCallLog.talk_duration_seconds)
            ).filter(
                VoiceCallLog.initiated_at >= cutoff_time,
                VoiceCallLog.talk_duration_seconds.isnot(None)
            ).scalar() or 0.0
            
            # Quality metrics
            quality_distribution = self.db_session.query(
                VoiceCallLog.audio_quality,
                func.count(VoiceCallLog.id).label('count')
            ).filter(
                VoiceCallLog.initiated_at >= cutoff_time,
                VoiceCallLog.audio_quality.isnot(None)
            ).group_by(VoiceCallLog.audio_quality).all()
            
            # Status distribution
            status_distribution = self.db_session.query(
                VoiceCallLog.status,
                func.count(VoiceCallLog.id).label('count')
            ).filter(
                VoiceCallLog.initiated_at >= cutoff_time
            ).group_by(VoiceCallLog.status).all()
            
            # Calculate rates
            answer_rate = (answered_calls / total_calls * 100) if total_calls > 0 else 0
            completion_rate = (completed_calls / total_calls * 100) if total_calls > 0 else 0
            
            return {
                'period_hours': hours,
                'total_calls': total_calls,
                'answered_calls': answered_calls,
                'completed_calls': completed_calls,
                'answer_rate': round(answer_rate, 2),
                'completion_rate': round(completion_rate, 2),
                'total_cost': round(total_cost, 4),
                'average_cost_per_call': round(total_cost / total_calls, 4) if total_calls > 0 else 0,
                'average_duration_seconds': round(avg_duration, 2),
                'average_talk_time_seconds': round(avg_talk_time, 2),
                'quality_distribution': {row.audio_quality: row.count for row in quality_distribution},
                'status_distribution': {row.status: row.count for row in status_distribution}
            }
            
        except Exception as e:
            logger.error(f"Failed to get call analytics: {str(e)}")
            return {}
    
    def get_employee_call_stats(self, employee_id: Optional[int] = None, hours: int = 24) -> List[Dict]:
        """
        Get call statistics by employee.
        
        Args:
            employee_id: Specific employee ID (optional)
            hours: Time period in hours
            
        Returns:
            List[Dict]: Employee call statistics
        """
        try:
            cutoff_time = datetime.now(timezone.utc) - timedelta(hours=hours)
            
            query = self.db_session.query(
                VoiceCallLog.employee_id,
                VoiceCallLog.employee_name,
                func.count(VoiceCallLog.id).label('total_calls'),
                func.sum(func.case([(VoiceCallLog.call_answered == 'true', 1)], else_=0)).label('answered_calls'),
                func.sum(func.case([(VoiceCallLog.status == 'completed', 1)], else_=0)).label('completed_calls'),
                func.sum(VoiceCallLog.total_cost).label('total_cost'),
                func.avg(VoiceCallLog.duration_seconds).label('avg_duration')
            ).filter(
                VoiceCallLog.initiated_at >= cutoff_time
            )
            
            if employee_id:
                query = query.filter(VoiceCallLog.employee_id == employee_id)
            
            results = query.group_by(
                VoiceCallLog.employee_id,
                VoiceCallLog.employee_name
            ).all()
            
            employee_stats = []
            for row in results:
                answer_rate = (row.answered_calls / row.total_calls * 100) if row.total_calls > 0 else 0
                completion_rate = (row.completed_calls / row.total_calls * 100) if row.total_calls > 0 else 0
                
                employee_stats.append({
                    'employee_id': row.employee_id,
                    'employee_name': row.employee_name,
                    'total_calls': row.total_calls,
                    'answered_calls': row.answered_calls,
                    'completed_calls': row.completed_calls,
                    'answer_rate': round(answer_rate, 2),
                    'completion_rate': round(completion_rate, 2),
                    'total_cost': round(row.total_cost or 0, 4),
                    'average_duration': round(row.avg_duration or 0, 2)
                })
            
            return employee_stats
            
        except Exception as e:
            logger.error(f"Failed to get employee call stats: {str(e)}")
            return []
    
    def get_failed_calls_for_retry(self) -> List[VoiceCallLog]:
        """
        Get calls that need to be retried.
        
        Returns:
            List[VoiceCallLog]: Calls ready for retry
        """
        try:
            now = datetime.now(timezone.utc)
            
            failed_calls = self.db_session.query(VoiceCallLog).filter(
                VoiceCallLog.status.in_(['failed', 'no_answer', 'pending_retry']),
                VoiceCallLog.retry_count < VoiceCallLog.max_retries,
                or_(
                    VoiceCallLog.next_retry_at.is_(None),
                    VoiceCallLog.next_retry_at <= now
                )
            ).all()
            
            return failed_calls
            
        except Exception as e:
            logger.error(f"Failed to get failed calls for retry: {str(e)}")
            return []
    
    def get_call_logs(self, 
                     email_log_id: Optional[int] = None,
                     employee_id: Optional[int] = None,
                     status: Optional[str] = None,
                     limit: int = 100,
                     offset: int = 0) -> List[VoiceCallLog]:
        """
        Get voice call logs with filtering.
        
        Args:
            email_log_id: Filter by email log ID
            employee_id: Filter by employee ID
            status: Filter by status
            limit: Maximum results
            offset: Pagination offset
            
        Returns:
            List[VoiceCallLog]: Filtered call logs
        """
        try:
            query = self.db_session.query(VoiceCallLog)
            
            if email_log_id:
                query = query.filter(VoiceCallLog.email_log_id == email_log_id)
            if employee_id:
                query = query.filter(VoiceCallLog.employee_id == employee_id)
            if status:
                query = query.filter(VoiceCallLog.status == status)
            
            call_logs = query.order_by(VoiceCallLog.initiated_at.desc()).offset(offset).limit(limit).all()
            return call_logs
            
        except Exception as e:
            logger.error(f"Failed to get call logs: {str(e)}")
            return []


def get_voice_call_analytics_service(db_session: Session) -> VoiceCallAnalyticsService:
    """
    Factory function to get the voice call analytics service.
    
    Args:
        db_session: Database session
        
    Returns:
        VoiceCallAnalyticsService: Voice call analytics service instance
    """
    return VoiceCallAnalyticsService(db_session)
