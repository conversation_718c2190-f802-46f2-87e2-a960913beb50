"""
VAPI (Voice AI Platform) integration for making voice calls to employees
when WhatsApp notifications are sent.
"""

import requests
import logging
from typing import List, Dict, Optional
from datetime import datetime, timezone, timedelta
from ..models import EmailLog

logger = logging.getLogger(__name__)


class VAPICaller:
    """
    VAPI integration for making voice calls to employees.
    """
    
    def __init__(self, 
                 api_key: str,
                 assistant_id: str,
                 phone_number_id: str = None):
        """
        Initialize the VAPI caller.
        
        Args:
            api_key: VAPI API key
            assistant_id: VAPI assistant ID for the voice calls
            phone_number_id: Optional phone number ID for outbound calls
        """
        self.api_key = api_key
        self.assistant_id = assistant_id
        self.phone_number_id = phone_number_id
        self.base_url = "https://api.vapi.ai"
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
    
    def make_call(self, recipient_phone: str, email_log: EmailLog, employee_name: str = None) -> Dict:
        """
        Make a voice call to an employee about a new email.
        
        Args:
            recipient_phone: Phone number to call (with country code)
            email_log: EmailLog instance containing email details
            employee_name: Optional employee name for personalization
            
        Returns:
            Dict: Call response with call_id and status
        """
        try:
            # Prepare call message
            call_message = self._format_call_message(email_log, employee_name)
            
            # VAPI call payload
            payload = {
                "assistantId": self.assistant_id,
                "customer": {
                    "number": recipient_phone
                },
                "assistantOverrides": {
                    "firstMessage": call_message,
                    "variableValues": {
                        "employeeName": employee_name or "Team Member",
                        "emailSender": email_log.sender.split('<')[0].strip() if '<' in email_log.sender else email_log.sender,
                        "emailSubject": email_log.subject or "No Subject",
                        "emailSummary": email_log.whatsapp_summary or "Email summary not available"
                    }
                }
            }
            
            # Add phone number ID if provided
            if self.phone_number_id:
                payload["phoneNumberId"] = self.phone_number_id
            
            response = requests.post(
                f"{self.base_url}/call",
                headers=self.headers,
                json=payload,
                timeout=30
            )
            
            if response.status_code == 201:
                call_data = response.json()
                logger.info(f"Successfully initiated VAPI call to {recipient_phone}: {call_data.get('id')}")
                return {
                    "success": True,
                    "call_id": call_data.get("id"),
                    "status": call_data.get("status", "initiated"),
                    "message": "Call initiated successfully"
                }
            else:
                error_msg = f"VAPI API error: {response.status_code} - {response.text}"
                logger.error(f"Failed to initiate call to {recipient_phone}: {error_msg}")
                return {
                    "success": False,
                    "error": error_msg,
                    "status": "failed"
                }
                
        except Exception as e:
            error_msg = f"Exception during VAPI call: {str(e)}"
            logger.error(f"Exception calling {recipient_phone}: {error_msg}")
            return {
                "success": False,
                "error": error_msg,
                "status": "failed"
            }
    
    def get_call_status(self, call_id: str) -> Dict:
        """
        Get the status of a VAPI call.
        
        Args:
            call_id: VAPI call ID
            
        Returns:
            Dict: Call status information
        """
        try:
            response = requests.get(
                f"{self.base_url}/call/{call_id}",
                headers=self.headers,
                timeout=30
            )
            
            if response.status_code == 200:
                call_data = response.json()
                return {
                    "success": True,
                    "status": call_data.get("status"),
                    "duration": call_data.get("duration"),
                    "cost": call_data.get("cost"),
                    "ended_reason": call_data.get("endedReason"),
                    "started_at": call_data.get("startedAt"),
                    "ended_at": call_data.get("endedAt")
                }
            else:
                error_msg = f"VAPI API error: {response.status_code} - {response.text}"
                logger.error(f"Failed to get call status for {call_id}: {error_msg}")
                return {
                    "success": False,
                    "error": error_msg
                }
                
        except Exception as e:
            error_msg = f"Exception getting call status: {str(e)}"
            logger.error(f"Exception getting status for call {call_id}: {error_msg}")
            return {
                "success": False,
                "error": error_msg
            }
    
    def _format_call_message(self, email_log: EmailLog, employee_name: str = None) -> str:
        """
        Format the voice message for the call.
        
        Args:
            email_log: EmailLog instance
            employee_name: Optional employee name
            
        Returns:
            str: Formatted voice message
        """
        greeting = f"Hello {employee_name}," if employee_name else "Hello,"
        sender = email_log.sender.split('<')[0].strip() if '<' in email_log.sender else email_log.sender
        
        message = f"""{greeting} This is an urgent notification from your Email Monitor Agent. 
        
You have received a new important email from {sender} with the subject: {email_log.subject or 'No Subject'}.

Here's a summary: {email_log.whatsapp_summary or 'Email summary not available'}

Please check your WhatsApp for the full details and take appropriate action. Thank you."""
        
        return message


def get_vapi_caller(config: Dict) -> VAPICaller:
    """
    Factory function to get the VAPI caller based on configuration.
    
    Args:
        config: Configuration dictionary containing:
            - vapi_api_key: VAPI API key
            - vapi_assistant_id: VAPI assistant ID
            - vapi_phone_number_id: (optional) VAPI phone number ID
        
    Returns:
        VAPICaller: VAPI caller instance
    """
    return VAPICaller(
        api_key=config['vapi_api_key'],
        assistant_id=config['vapi_assistant_id'],
        phone_number_id=config.get('vapi_phone_number_id')
    )
