<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>Email Monitor Agent Dashboard</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet">
    <link rel="stylesheet" href="css/styles.css">
    <style>
        /* Enhanced Rich UI/UX Styles - Your Custom Color Palette */
        :root {
            /* Your Specified Colors */
            --bright-orange: #F47C20;
            --white: #FFFFFF;
            --light-blue: #0B2A5A;
            --medium-gray: #6E7C8E;
            --dark-gray: #2E2E2E;

            /* Derived Colors */
            --orange-light: rgba(244, 124, 32, 0.1);
            --orange-medium: rgba(244, 124, 32, 0.3);
            --shadow-glow: 0 8px 32px rgba(244, 124, 32, 0.15);
            --shadow-deep: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        /* Premium Typography */
        body {
            font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif !important;
            font-weight: 400;
            letter-spacing: -0.01em;
            background-color: var(--white) !important;
            color: var(--dark-gray) !important;
        }

        h1, h2, h3, h4, h5, h6 {
            font-family: 'Poppins', sans-serif !important;
            font-weight: 600;
            letter-spacing: -0.02em;
            color: var(--dark-gray) !important;
        }

        .monospace {
            font-family: 'JetBrains Mono', monospace !important;
        }

        /* Enhanced Navigation - Light Blue Header */
        .navbar {
            background: var(--light-blue) !important;
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 8px 32px rgba(11, 42, 90, 0.2);
        }

        .nav-brand {
            font-weight: 700 !important;
            font-size: 1.4rem !important;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            color: var(--white) !important;
        }

        .nav-brand i {
            color: var(--bright-orange) !important;
            filter: drop-shadow(0 2px 4px var(--orange-medium));
            animation: pulse-glow 3s ease-in-out infinite;
        }

        @keyframes pulse-glow {
            0%, 100% { filter: drop-shadow(0 2px 4px var(--orange-medium)); }
            50% { filter: drop-shadow(0 2px 8px var(--orange-medium)); }
        }

        .nav-link {
            font-weight: 500 !important;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
            position: relative;
            overflow: hidden;
            color: var(--white) !important;
        }

        .nav-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transition: left 0.5s;
        }

        .nav-link:hover::before {
            left: 100%;
        }

        .nav-link:hover, .nav-link.active {
            background: var(--bright-orange) !important;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px var(--orange-medium);
            color: var(--white) !important;
        }

        /* Enhanced Buttons - Bright Orange */
        .btn-primary {
            background: var(--bright-orange) !important;
            border: none !important;
            font-weight: 600 !important;
            letter-spacing: 0.025em !important;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
            position: relative;
            overflow: hidden;
            color: var(--white) !important;
        }

        .btn-primary::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .btn-primary:hover::before {
            left: 100%;
        }

        .btn-primary:hover {
            transform: translateY(-2px) !important;
            box-shadow: var(--shadow-glow) !important;
            background: var(--bright-orange) !important;
            color: var(--white) !important;
        }

        .btn-secondary {
            background: var(--medium-gray) !important;
            border: none !important;
            color: var(--white) !important;
            font-weight: 500 !important;
        }

        .btn-secondary:hover {
            background: var(--dark-gray) !important;
            color: var(--white) !important;
        }

        /* Enhanced Cards - White Background */
        .stat-card {
            background: var(--white) !important;
            border: 1px solid var(--orange-light) !important;
            border-left: 4px solid var(--bright-orange) !important;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
            position: relative;
            overflow: hidden;
        }

        .stat-card::after {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 100px;
            height: 100px;
            background: radial-gradient(circle, var(--orange-light) 0%, transparent 70%);
            transform: translate(30px, -30px);
        }

        .stat-card:hover {
            transform: translateY(-4px) !important;
            box-shadow: var(--shadow-deep) !important;
            border-color: var(--orange-medium) !important;
        }

        /* Enhanced Welcome Message - Bright Orange */
        .welcome-message {
            background: var(--bright-orange) !important;
            color: var(--white) !important;
            border: none !important;
            box-shadow: var(--shadow-glow) !important;
            position: relative;
            overflow: hidden;
        }

        .welcome-message::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translate(-50%, -50%) rotate(0deg); }
            50% { transform: translate(-50%, -50%) rotate(180deg); }
        }

        .welcome-message h2 {
            font-weight: 700 !important;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            color: var(--white) !important;
        }

        /* Enhanced Card Headers - Light Blue */
        .card-header {
            background: var(--light-blue) !important;
            color: var(--white) !important;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
        }

        .card-header h3 {
            font-weight: 600 !important;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
            color: var(--white) !important;
        }

        .card-header h3 i {
            color: var(--bright-orange) !important;
            filter: drop-shadow(0 1px 2px var(--orange-medium));
        }

        /* Enhanced Page Headers - Dark Gray Text */
        .page-header h1 {
            font-weight: 700 !important;
            font-size: 2.5rem !important;
            color: var(--dark-gray) !important;
        }

        .page-header h1 i {
            color: var(--bright-orange) !important;
            filter: drop-shadow(0 2px 4px var(--orange-medium));
        }

        .page-header p {
            color: var(--medium-gray) !important;
            font-weight: 400 !important;
        }

        /* Enhanced Animations */
        .animate-fade-in {
            animation: fadeInUp 0.6s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Enhanced Status Indicator */
        .status-indicator {
            background: rgba(255, 255, 255, 0.15) !important;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            font-weight: 500;
        }

        /* Enhanced Scrollbar - Orange Theme */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--orange-light);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb {
            background: var(--bright-orange);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--dark-gray);
        }

        /* Enhanced Page Header */
        .page-header {
            display: flex !important;
            justify-content: space-between !important;
            align-items: flex-start !important;
            margin-bottom: 2rem !important;
        }

        .page-header-actions {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        /* Enhanced Welcome Message */
        .welcome-content {
            position: relative;
            z-index: 2;
        }

        .welcome-features {
            display: flex;
            gap: 1rem;
            margin-top: 1.5rem;
            flex-wrap: wrap;
        }

        .feature-badge {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 25px;
            font-size: 0.875rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }

        .feature-badge:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        /* Enhanced Stat Cards - Your Color Palette */
        .stat-trend {
            font-size: 0.75rem;
            color: var(--medium-gray);
            margin-top: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.25rem;
            font-weight: 500;
        }

        .stat-trend i {
            color: var(--bright-orange);
        }

        .stat-content h3 {
            font-size: 2.5rem !important;
            font-weight: 800 !important;
            margin-bottom: 0.5rem !important;
            color: var(--dark-gray) !important;
        }

        .stat-content p {
            font-weight: 600 !important;
            color: var(--medium-gray) !important;
            margin-bottom: 0.75rem !important;
        }

        /* Enhanced Dashboard Cards - White Background */
        .dashboard-card {
            background: var(--white) !important;
            border: 1px solid var(--orange-light) !important;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
        }

        .dashboard-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 28px rgba(0, 0, 0, 0.12);
        }

        /* Enhanced Form Elements - Your Color Palette */
        .filter-select, .filter-input {
            font-family: 'Inter', sans-serif !important;
            font-weight: 500 !important;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
            border: 1px solid var(--medium-gray) !important;
            color: var(--dark-gray) !important;
        }

        .filter-select:focus, .filter-input:focus {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px var(--orange-medium) !important;
            border-color: var(--bright-orange) !important;
        }

        /* Enhanced Modal - White Background */
        .modal-content {
            background: var(--white) !important;
            border: 1px solid var(--orange-light) !important;
        }

        /* Enhanced Loading */
        .loading-spinner {
            background: var(--white);
            padding: 2rem;
            border-radius: 1rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            color: var(--dark-gray);
        }

        /* Enhanced Toast */
        .toast {
            background: var(--white) !important;
            border: 1px solid var(--orange-light) !important;
            backdrop-filter: blur(20px);
            color: var(--dark-gray) !important;
        }

        /* Responsive Enhancements */
        @media (max-width: 768px) {
            .page-header {
                flex-direction: column !important;
                align-items: flex-start !important;
                gap: 1rem !important;
            }

            .welcome-features {
                justify-content: center;
            }

            .feature-badge {
                font-size: 0.75rem;
                padding: 0.375rem 0.75rem;
            }

            .stat-content h3 {
                font-size: 2rem !important;
            }
        }

        /* Status Summary - Your Color Palette */
        .status-summary {
            margin-top: 1.5rem;
            padding-top: 1rem;
            border-top: 1px solid var(--orange-light);
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }

        .summary-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
        }

        .summary-label {
            font-size: 0.75rem;
            color: var(--medium-gray);
            font-weight: 500;
            margin-bottom: 0.25rem;
        }

        .summary-value {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--bright-orange);
        }

        /* Full Page Employee Layout - Exact Design Match */
        .employees-container {
            background: var(--white) !important;
            border-radius: 12px !important;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05) !important;
            padding: 1.5rem !important;
            margin-top: 2rem !important;
            min-height: calc(100vh - 300px) !important;
            width: 100% !important;
            max-width: none !important;
            display: block !important;
            grid-template-columns: none !important;
        }

        .employees-list {
            display: flex;
            flex-direction: column;
            gap: 0;
            padding: 0;
            width: 100%;
        }

        .employee-card {
            background: var(--white) !important;
            border: none !important;
            border-bottom: 1px solid #E5E7EB !important;
            border-radius: 0 !important;
            padding: 0 !important;
            box-shadow: none !important;
            transition: all 0.2s ease !important;
            position: relative;
        }

        .employee-card:hover {
            background: #F9FAFB !important;
        }

        .employee-content {
            display: flex !important;
            align-items: center !important;
            padding: 1rem 1.5rem !important;
            gap: 1rem !important;
        }

        .employee-avatar {
            width: 40px !important;
            height: 40px !important;
            border-radius: 50% !important;
            background: var(--bright-orange) !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            color: var(--white) !important;
            font-size: 0.875rem !important;
            font-weight: 600 !important;
            font-family: 'Poppins', sans-serif !important;
            flex-shrink: 0;
            box-shadow: 0 2px 8px rgba(244, 124, 32, 0.2) !important;
        }

        .employee-info {
            flex: 1 !important;
            min-width: 0 !important;
            display: flex !important;
            flex-direction: column !important;
            gap: 0.25rem !important;
        }

        .employee-info h3 {
            font-size: 0.875rem !important;
            font-weight: 500 !important;
            color: var(--dark-gray) !important;
            margin: 0 !important;
            font-family: 'Poppins', sans-serif !important;
            line-height: 1.2 !important;
        }

        .employee-phone {
            display: flex !important;
            align-items: center !important;
            gap: 0.25rem !important;
            color: var(--medium-gray) !important;
            font-size: 0.75rem !important;
            font-weight: 400 !important;
            margin: 0 !important;
        }

        .employee-phone i {
            color: #25d366 !important;
            font-size: 0.75rem !important;
        }

        .employee-actions {
            display: flex !important;
            gap: 0.5rem !important;
            flex-shrink: 0 !important;
            align-items: center !important;
        }

        .btn-icon {
            width: 24px !important;
            height: 24px !important;
            border: none !important;
            border-radius: 4px !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            cursor: pointer !important;
            transition: all 0.2s ease !important;
            font-size: 0.75rem !important;
            background: transparent !important;
        }

        .btn-icon.edit {
            color: var(--bright-orange) !important;
        }

        .btn-icon.edit:hover {
            background: var(--orange-light) !important;
        }

        .btn-icon.delete {
            color: #ef4444 !important;
        }

        .btn-icon.delete:hover {
            background: rgba(239, 68, 68, 0.1) !important;
        }

        /* Enhanced Statistics Dashboard */
        .enhanced-stats-dashboard {
            display: flex;
            flex-direction: column;
            gap: 2rem;
            margin-top: 2rem;
            min-height: calc(100vh - 350px);
            width: 100%;
        }

        /* Welcome Banner */
        .welcome-banner {
            background: linear-gradient(135deg, var(--bright-orange) 0%, rgba(244, 124, 32, 0.8) 100%);
            border-radius: 16px;
            padding: 2.5rem;
            color: var(--white);
            box-shadow: 0 8px 32px rgba(244, 124, 32, 0.3);
            position: relative;
            overflow: hidden;
        }

        .welcome-banner::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 200px;
            height: 200px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            transform: translate(50%, -50%);
        }

        .banner-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
            z-index: 1;
        }

        .banner-text h2 {
            font-size: 1.75rem;
            font-weight: 700;
            margin: 0 0 1rem 0;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .banner-text h2 i {
            font-size: 1.5rem;
        }

        .banner-text p {
            font-size: 1rem;
            margin: 0 0 1.5rem 0;
            line-height: 1.6;
            opacity: 0.95;
        }

        .banner-features {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .feature-badge {
            background: rgba(255, 255, 255, 0.2);
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            backdrop-filter: blur(10px);
        }

        .banner-actions .btn-primary {
            background: var(--white) !important;
            color: var(--bright-orange) !important;
            border: none !important;
            padding: 0.75rem 1.5rem !important;
            font-weight: 600 !important;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1) !important;
        }

        .banner-actions .btn-primary:hover {
            transform: translateY(-2px) !important;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15) !important;
        }

        /* Enhanced Statistics Cards */
        .enhanced-stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }

        .enhanced-stat-card {
            background: var(--white);
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(244, 124, 32, 0.1);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .enhanced-stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--bright-orange) 0%, rgba(244, 124, 32, 0.7) 100%);
        }

        .enhanced-stat-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 16px 48px rgba(244, 124, 32, 0.15);
            border-color: var(--bright-orange);
        }

        .stat-card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .enhanced-stat-card .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: var(--white);
            position: relative;
            overflow: hidden;
        }

        .enhanced-stat-card.emails .stat-icon {
            background: linear-gradient(135deg, var(--bright-orange) 0%, rgba(244, 124, 32, 0.8) 100%);
        }

        .enhanced-stat-card.whatsapp .stat-icon {
            background: linear-gradient(135deg, #25d366 0%, #128c7e 100%);
        }

        .enhanced-stat-card.replies .stat-icon {
            background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
        }

        .enhanced-stat-card.employees .stat-icon {
            background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
        }

        .stat-badge {
            background: var(--orange-light);
            color: var(--bright-orange);
            padding: 0.375rem 0.75rem;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 600;
        }

        .stat-content {
            margin-bottom: 1.5rem;
        }

        .stat-number {
            font-size: 3rem;
            font-weight: 800;
            color: var(--dark-gray);
            margin: 0 0 0.5rem 0;
            font-family: 'JetBrains Mono', monospace;
            line-height: 1;
        }

        .stat-label {
            font-size: 1rem;
            color: var(--medium-gray);
            font-weight: 500;
            margin: 0 0 1rem 0;
        }

        .stat-progress {
            margin-top: 1rem;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(244, 124, 32, 0.1);
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 0.5rem;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--bright-orange) 0%, rgba(244, 124, 32, 0.7) 100%);
            border-radius: 4px;
            transition: width 1s ease;
            position: relative;
        }

        .progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .progress-text {
            font-size: 0.75rem;
            color: var(--medium-gray);
            font-weight: 500;
        }

        .stat-footer {
            border-top: 1px solid rgba(244, 124, 32, 0.1);
            padding-top: 1rem;
        }

        .stat-trend {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.875rem;
            font-weight: 500;
        }

        .stat-trend.positive {
            color: #22c55e;
        }

        .stat-trend.negative {
            color: #ef4444;
        }

        .stat-trend.stable {
            color: var(--medium-gray);
        }

        .stat-trend i {
            font-size: 0.75rem;
        }

        /* System Overview Grid */
        .overview-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
        }

        .overview-card {
            background: var(--white);
            border: 2px solid rgba(244, 124, 32, 0.1);
            border-radius: 12px;
            padding: 1.5rem;
            transition: all 0.3s ease;
        }

        .overview-card:hover {
            border-color: var(--bright-orange);
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(244, 124, 32, 0.1);
        }

        .overview-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid rgba(244, 124, 32, 0.1);
        }

        .overview-header h3 {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--dark-gray);
            margin: 0;
        }

        .status-indicator.active {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.875rem;
            font-weight: 500;
            color: #22c55e;
        }

        .status-indicator.active i {
            font-size: 0.75rem;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .overview-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 1rem;
        }

        .stat-item {
            text-align: center;
        }

        .stat-label {
            font-size: 0.75rem;
            color: var(--medium-gray);
            font-weight: 500;
            margin-bottom: 0.5rem;
            display: block;
        }

        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--bright-orange);
            font-family: 'JetBrains Mono', monospace;
        }

        /* Activity Feed */
        .activity-feed {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }

        .activity-item {
            display: flex;
            gap: 1rem;
            padding: 1.5rem;
            background: var(--white);
            border: 1px solid rgba(244, 124, 32, 0.1);
            border-radius: 12px;
            transition: all 0.3s ease;
        }

        .activity-item:hover {
            border-color: var(--bright-orange);
            transform: translateX(4px);
            box-shadow: 0 4px 16px rgba(244, 124, 32, 0.1);
        }

        .activity-icon {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.25rem;
            flex-shrink: 0;
            color: var(--white);
        }

        .activity-icon.email-icon {
            background: var(--bright-orange);
        }

        .activity-icon.whatsapp-icon {
            background: #25d366;
        }

        .activity-icon.reply-icon {
            background: #0ea5e9;
        }

        .activity-icon.system-icon {
            background: var(--medium-gray);
        }

        .activity-icon.employee-icon {
            background: #8b5cf6;
        }

        .activity-content {
            flex: 1;
        }

        .activity-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
        }

        .activity-header h4 {
            font-size: 1rem;
            font-weight: 600;
            color: var(--dark-gray);
            margin: 0;
        }

        .activity-time {
            font-size: 0.75rem;
            color: var(--medium-gray);
            font-weight: 500;
        }

        .activity-content p {
            font-size: 0.875rem;
            color: var(--medium-gray);
            margin: 0 0 1rem 0;
            line-height: 1.5;
        }

        .activity-tags {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .tag {
            font-size: 0.75rem;
            font-weight: 500;
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            border: 1px solid;
        }

        .tag.success {
            background: rgba(34, 197, 94, 0.1);
            color: #22c55e;
            border-color: rgba(34, 197, 94, 0.2);
        }

        .tag.info {
            background: rgba(14, 165, 233, 0.1);
            color: #0ea5e9;
            border-color: rgba(14, 165, 233, 0.2);
        }

        .tag.warning {
            background: rgba(245, 158, 11, 0.1);
            color: #f59e0b;
            border-color: rgba(245, 158, 11, 0.2);
        }

        /* Performance Charts */
        .performance-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 1.5rem;
        }

        .performance-card {
            background: var(--white);
            border: 2px solid rgba(244, 124, 32, 0.1);
            border-radius: 12px;
            padding: 1.5rem;
            transition: all 0.3s ease;
        }

        .performance-card:hover {
            border-color: var(--bright-orange);
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(244, 124, 32, 0.1);
        }

        .performance-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid rgba(244, 124, 32, 0.1);
        }

        .performance-header h3 {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--dark-gray);
            margin: 0;
        }

        .trend-indicator {
            font-size: 0.875rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }

        .trend-indicator.up {
            color: #22c55e;
        }

        .trend-indicator.down {
            color: #ef4444;
        }

        .trend-indicator.stable {
            color: var(--medium-gray);
        }

        /* Chart Styles */
        .performance-chart {
            height: 200px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .chart-bars {
            display: flex;
            align-items: end;
            justify-content: space-between;
            height: 150px;
            gap: 0.5rem;
            padding: 0 0.5rem;
        }

        .chart-bar {
            flex: 1;
            background: linear-gradient(180deg, var(--bright-orange) 0%, rgba(244, 124, 32, 0.7) 100%);
            border-radius: 4px 4px 0 0;
            min-height: 20px;
            transition: all 0.3s ease;
        }

        .chart-bar:hover {
            background: var(--bright-orange);
            transform: scaleY(1.05);
        }

        .chart-labels {
            display: flex;
            justify-content: space-between;
            padding: 0.5rem;
            font-size: 0.75rem;
            color: var(--medium-gray);
            font-weight: 500;
        }

        /* Performance Metrics */
        .performance-metrics {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .metric-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem;
            background: rgba(244, 124, 32, 0.05);
            border-radius: 8px;
            border-left: 4px solid var(--bright-orange);
        }

        .metric-label {
            font-size: 0.875rem;
            color: var(--medium-gray);
            font-weight: 500;
        }

        .metric-value {
            font-size: 1rem;
            font-weight: 700;
            color: var(--bright-orange);
            font-family: 'JetBrains Mono', monospace;
        }

        /* Responsive Design for Enhanced Stats Dashboard */
        @media (max-width: 768px) {
            .enhanced-stats-dashboard {
                gap: 1.5rem;
                margin-top: 1.5rem;
            }

            .welcome-banner {
                padding: 2rem;
            }

            .banner-content {
                flex-direction: column;
                text-align: center;
                gap: 1.5rem;
            }

            .banner-text h2 {
                font-size: 1.5rem;
            }

            .banner-text p {
                font-size: 0.9rem;
            }

            .banner-features {
                justify-content: center;
            }

            .feature-badge {
                font-size: 0.75rem;
                padding: 0.375rem 0.75rem;
            }

            .enhanced-stats-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }

            .enhanced-stat-card {
                padding: 1.5rem;
            }

            .enhanced-stat-card .stat-icon {
                width: 50px;
                height: 50px;
                font-size: 1.25rem;
            }

            .stat-number {
                font-size: 2.5rem;
            }

            .stat-label {
                font-size: 0.9rem;
            }

            .stat-badge {
                font-size: 0.7rem;
                padding: 0.25rem 0.5rem;
            }

            .progress-bar {
                height: 6px;
            }

            .stat-trend {
                font-size: 0.8rem;
            }
        }

        /* Additional Responsive Styles for Employee Cards */
        @media (max-width: 768px) {
            .employee-content {
                padding: 0.875rem 1rem !important;
                gap: 0.75rem !important;
            }

            .employee-avatar {
                width: 40px !important;
                height: 40px !important;
                font-size: 1rem !important;
            }

            .employee-info h3 {
                font-size: 0.9rem !important;
            }

            .employee-phone {
                font-size: 0.8rem !important;
            }

            .btn-icon {
                width: 24px !important;
                height: 24px !important;
                font-size: 0.7rem !important;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <i class="fas fa-envelope-open-text"></i>
                <span>Email Monitor Agent</span>
            </div>
            <div class="nav-menu">
                <a href="#dashboard" class="nav-link active" data-tab="dashboard">
                    <i class="fas fa-chart-line"></i> Dashboard
                </a>
                <a href="#emails" class="nav-link" data-tab="emails">
                    <i class="fas fa-inbox"></i> Email Logs
                </a>
                <a href="#employees" class="nav-link" data-tab="employees">
                    <i class="fas fa-users"></i> Employees
                </a>
                <a href="#settings" class="nav-link" data-tab="settings">
                    <i class="fas fa-cog"></i> Settings
                </a>
            </div>
            <div class="nav-status">
                <div class="status-indicator" id="agentStatus">
                    <i class="fas fa-circle"></i>
                    <span>Agent Status</span>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Dashboard Tab -->
        <div id="dashboard" class="tab-content active animate-fade-in">
            <div class="page-header animate__animated animate__fadeInDown">
                <div>
                    <h1><i class="fas fa-chart-line"></i> Dashboard</h1>
                    <p>Monitor your email processing and system performance with real-time insights</p>
                </div>
                <div class="page-header-actions">
                    <button class="btn-primary" onclick="refreshDashboard()">
                        <i class="fas fa-sync-alt"></i> Refresh Data
                    </button>
                </div>
            </div>

            <!-- Welcome Message -->
            <div class="welcome-message animate__animated animate__fadeInUp">
                <div class="welcome-content">
                    <h2><i class="fas fa-rocket"></i> Welcome to Your Email Monitor Agent Dashboard!</h2>
                    <p>Your intelligent email monitoring system is actively running. Track performance, manage notifications, and optimize your workflow with powerful AI-driven insights.</p>
                    <div class="welcome-features">
                        <span class="feature-badge"><i class="fas fa-brain"></i> AI-Powered</span>
                        <span class="feature-badge"><i class="fas fa-shield-alt"></i> Secure</span>
                        <span class="feature-badge"><i class="fas fa-clock"></i> Real-time</span>
                    </div>
                </div>
            </div>

            <!-- Stats Cards -->
            <div class="stats-grid">
                <div class="stat-card animate__animated animate__fadeInUp" style="animation-delay: 0.1s">
                    <div class="stat-icon emails">
                        <i class="fas fa-envelope"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="totalEmails" class="monospace">15</h3>
                        <p>Total Emails Processed</p>
                        <span class="stat-change positive" id="emailsChange">+4 today</span>
                        <div class="stat-trend">
                            <i class="fas fa-arrow-up"></i> 12% increase
                        </div>
                    </div>
                </div>

                <div class="stat-card animate__animated animate__fadeInUp" style="animation-delay: 0.2s">
                    <div class="stat-icon whatsapp">
                        <i class="fab fa-whatsapp"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="totalNotifications" class="monospace">12</h3>
                        <p>WhatsApp Notifications</p>
                        <span class="stat-change positive" id="notificationsChange">+3 today</span>
                        <div class="stat-trend">
                            <i class="fas fa-arrow-up"></i> 8% increase
                        </div>
                    </div>
                </div>

                <div class="stat-card animate__animated animate__fadeInUp" style="animation-delay: 0.3s">
                    <div class="stat-icon replies">
                        <i class="fas fa-reply"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="totalReplies" class="monospace">8</h3>
                        <p>Auto Replies Sent</p>
                        <span class="stat-change positive" id="repliesChange">+2 today</span>
                        <div class="stat-trend">
                            <i class="fas fa-arrow-up"></i> 5% increase
                        </div>
                    </div>
                </div>

                <div class="stat-card animate__animated animate__fadeInUp" style="animation-delay: 0.4s">
                    <div class="stat-icon employees">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="totalEmployees" class="monospace">3</h3>
                        <p>Active Employees</p>
                        <span class="stat-change neutral" id="employeesChange">All Online</span>
                        <div class="stat-trend">
                            <i class="fas fa-check-circle"></i> 100% active
                        </div>
                    </div>
                </div>
            </div>

            <!-- Enhanced Statistics Dashboard -->
            <div class="enhanced-stats-dashboard animate__animated animate__fadeInUp" style="animation-delay: 0.5s">
                <!-- Welcome Banner -->
                <div class="welcome-banner">
                    <div class="banner-content">
                        <div class="banner-text">
                            <h2><i class="fas fa-rocket"></i> Welcome to Your Email Monitor Agent Dashboard!</h2>
                            <p>Your intelligent email monitoring system is actively running. Track performance, manage notifications, and optimize your workflow with powerful AI-driven insights.</p>
                            <div class="banner-features">
                                <span class="feature-badge"><i class="fas fa-brain"></i> AI-Powered</span>
                                <span class="feature-badge"><i class="fas fa-shield-alt"></i> Secure</span>
                                <span class="feature-badge"><i class="fas fa-clock"></i> Real-time</span>
                            </div>
                        </div>
                        <div class="banner-actions">
                            <button class="btn-primary" onclick="refreshDashboard()">
                                <i class="fas fa-sync-alt"></i> Refresh Data
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Enhanced Statistics Cards -->
                <div class="enhanced-stats-grid">
                    <div class="enhanced-stat-card emails animate__animated animate__fadeInUp" style="animation-delay: 0.1s">
                        <div class="stat-card-header">
                            <div class="stat-icon">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <div class="stat-badge">
                                <span>+4 today</span>
                            </div>
                        </div>
                        <div class="stat-content">
                            <h3 id="totalEmails" class="stat-number">15</h3>
                            <p class="stat-label">Total Emails Processed</p>
                            <div class="stat-progress">
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: 75%"></div>
                                </div>
                                <span class="progress-text">75% increase</span>
                            </div>
                        </div>
                        <div class="stat-footer">
                            <div class="stat-trend positive">
                                <i class="fas fa-arrow-up"></i>
                                <span>12% from last week</span>
                            </div>
                        </div>
                    </div>

                    <div class="enhanced-stat-card whatsapp animate__animated animate__fadeInUp" style="animation-delay: 0.2s">
                        <div class="stat-card-header">
                            <div class="stat-icon">
                                <i class="fab fa-whatsapp"></i>
                            </div>
                            <div class="stat-badge">
                                <span>+3 today</span>
                            </div>
                        </div>
                        <div class="stat-content">
                            <h3 id="totalNotifications" class="stat-number">12</h3>
                            <p class="stat-label">WhatsApp Notifications</p>
                            <div class="stat-progress">
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: 80%"></div>
                                </div>
                                <span class="progress-text">80% increase</span>
                            </div>
                        </div>
                        <div class="stat-footer">
                            <div class="stat-trend positive">
                                <i class="fas fa-arrow-up"></i>
                                <span>8% from last week</span>
                            </div>
                        </div>
                    </div>

                    <div class="enhanced-stat-card replies animate__animated animate__fadeInUp" style="animation-delay: 0.3s">
                        <div class="stat-card-header">
                            <div class="stat-icon">
                                <i class="fas fa-reply"></i>
                            </div>
                            <div class="stat-badge">
                                <span>+2 today</span>
                            </div>
                        </div>
                        <div class="stat-content">
                            <h3 id="totalReplies" class="stat-number">8</h3>
                            <p class="stat-label">Auto Replies Sent</p>
                            <div class="stat-progress">
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: 65%"></div>
                                </div>
                                <span class="progress-text">65% increase</span>
                            </div>
                        </div>
                        <div class="stat-footer">
                            <div class="stat-trend positive">
                                <i class="fas fa-arrow-up"></i>
                                <span>5% from last week</span>
                            </div>
                        </div>
                    </div>

                    <div class="enhanced-stat-card employees animate__animated animate__fadeInUp" style="animation-delay: 0.4s">
                        <div class="stat-card-header">
                            <div class="stat-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="stat-badge">
                                <span>All Online</span>
                            </div>
                        </div>
                        <div class="stat-content">
                            <h3 id="totalEmployees" class="stat-number">3</h3>
                            <p class="stat-label">Active Employees</p>
                            <div class="stat-progress">
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: 100%"></div>
                                </div>
                                <span class="progress-text">100% active</span>
                            </div>
                        </div>
                        <div class="stat-footer">
                            <div class="stat-trend stable">
                                <i class="fas fa-check-circle"></i>
                                <span>All systems operational</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>

        <!-- Email Logs Tab -->
        <div id="emails" class="tab-content">
            <div class="page-header">
                <h1><i class="fas fa-inbox"></i> Email Logs</h1>
                <p>View and manage processed emails with AI summaries</p>
            </div>

            <!-- Filters -->
            <div class="filters-section">
                <div class="filters-grid">
                    <div class="filter-group">
                        <label for="dateFilter">Date Range</label>
                        <select id="dateFilter" class="filter-select">
                            <option value="today">Today</option>
                            <option value="week">This Week</option>
                            <option value="month">This Month</option>
                            <option value="all">All Time</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label for="senderFilter">Sender</label>
                        <input type="text" id="senderFilter" class="filter-input" placeholder="Filter by sender...">
                    </div>
                    <div class="filter-group">
                        <label for="statusFilter">Status</label>
                        <select id="statusFilter" class="filter-select">
                            <option value="all">All Status</option>
                            <option value="processed">Processed</option>
                            <option value="pending">Pending</option>
                            <option value="failed">Failed</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <button class="btn-primary" onclick="applyFilters()">
                            <i class="fas fa-filter"></i> Apply Filters
                        </button>
                        <button class="btn-secondary" onclick="clearFilters()">
                            <i class="fas fa-times"></i> Clear
                        </button>
                    </div>
                </div>
            </div>

            <!-- Email List -->
            <div class="emails-container">
                <div id="emailsList" class="emails-list">
                    <!-- Email items will be loaded here -->
                </div>
                <div class="pagination" id="emailsPagination">
                    <!-- Pagination will be loaded here -->
                </div>
            </div>
        </div>

        <!-- Employees Tab -->
        <div id="employees" class="tab-content">
            <div class="page-header animate__animated animate__fadeInDown">
                <div>
                    <h1><i class="fas fa-users"></i> Employee Management</h1>
                    <p>Manage WhatsApp recipients for email notifications</p>
                </div>
                <div class="page-header-actions">
                    <button class="btn-primary" onclick="openAddEmployeeModal()">
                        <i class="fas fa-plus"></i> Add Employee
                    </button>
                </div>
            </div>

            <!-- Employees List -->
            <div class="employees-container animate__animated animate__fadeInUp">
                <div id="employeesList" class="employees-list">
                    <!-- Simple Employee Cards - Exact Match -->
                    <div class="employee-card animate__animated animate__fadeInUp" style="animation-delay: 0.1s">
                        <div class="employee-content">
                            <div class="employee-avatar">
                                <span>VG</span>
                            </div>
                            <div class="employee-info">
                                <h3>Vishnu Bala Guru</h3>
                                <div class="employee-phone">
                                    <i class="fab fa-whatsapp"></i>
                                    <span>+917598638873</span>
                                </div>
                            </div>
                            <div class="employee-actions">
                                <button class="btn-icon edit" onclick="editEmployee(1)" title="Edit Employee">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn-icon delete" onclick="deleteEmployee(1)" title="Delete Employee">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="employee-card animate__animated animate__fadeInUp" style="animation-delay: 0.2s">
                        <div class="employee-content">
                            <div class="employee-avatar">
                                <span>T</span>
                            </div>
                            <div class="employee-info">
                                <h3>T3</h3>
                                <div class="employee-phone">
                                    <i class="fab fa-whatsapp"></i>
                                    <span>+918778869983</span>
                                </div>
                            </div>
                            <div class="employee-actions">
                                <button class="btn-icon edit" onclick="editEmployee(2)" title="Edit Employee">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn-icon delete" onclick="deleteEmployee(2)" title="Delete Employee">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="employee-card animate__animated animate__fadeInUp" style="animation-delay: 0.3s">
                        <div class="employee-content">
                            <div class="employee-avatar">
                                <span>JD</span>
                            </div>
                            <div class="employee-info">
                                <h3>John Doe</h3>
                                <div class="employee-phone">
                                    <i class="fab fa-whatsapp"></i>
                                    <span>+1234567890</span>
                                </div>
                            </div>
                            <div class="employee-actions">
                                <button class="btn-icon edit" onclick="editEmployee(3)" title="Edit Employee">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn-icon delete" onclick="deleteEmployee(3)" title="Delete Employee">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="employee-card animate__animated animate__fadeInUp" style="animation-delay: 0.4s">
                        <div class="employee-content">
                            <div class="employee-avatar">
                                <span>AS</span>
                            </div>
                            <div class="employee-info">
                                <h3>Alice Smith</h3>
                                <div class="employee-phone">
                                    <i class="fab fa-whatsapp"></i>
                                    <span>+447123456789</span>
                                </div>
                            </div>
                            <div class="employee-actions">
                                <button class="btn-icon edit" onclick="editEmployee(4)" title="Edit Employee">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn-icon delete" onclick="deleteEmployee(4)" title="Delete Employee">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="employee-card animate__animated animate__fadeInUp" style="animation-delay: 0.5s">
                        <div class="employee-content">
                            <div class="employee-avatar">
                                <span>MJ</span>
                            </div>
                            <div class="employee-info">
                                <h3>Michael Johnson</h3>
                                <div class="employee-phone">
                                    <i class="fab fa-whatsapp"></i>
                                    <span>+61987654321</span>
                                </div>
                            </div>
                            <div class="employee-actions">
                                <button class="btn-icon edit" onclick="editEmployee(5)" title="Edit Employee">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn-icon delete" onclick="deleteEmployee(5)" title="Delete Employee">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="employee-card animate__animated animate__fadeInUp" style="animation-delay: 0.6s">
                        <div class="employee-content">
                            <div class="employee-avatar">
                                <span>SK</span>
                            </div>
                            <div class="employee-info">
                                <h3>Sarah Kim</h3>
                                <div class="employee-phone">
                                    <i class="fab fa-whatsapp"></i>
                                    <span>+821012345678</span>
                                </div>
                            </div>
                            <div class="employee-actions">
                                <button class="btn-icon edit" onclick="editEmployee(6)" title="Edit Employee">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn-icon delete" onclick="deleteEmployee(6)" title="Delete Employee">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Settings Tab -->
        <div id="settings" class="tab-content">
            <div class="page-header">
                <h1><i class="fas fa-cog"></i> Settings</h1>
                <p>Configure your Email Monitor Agent</p>
            </div>

            <div class="settings-grid">
                <div class="settings-card">
                    <h3><i class="fas fa-envelope"></i> Email Configuration</h3>
                    <div class="setting-item">
                        <label>Monitoring Email</label>
                        <input type="email" id="monitoringEmail" readonly>
                    </div>
                    <div class="setting-item">
                        <label>Allowed Sender</label>
                        <input type="email" id="allowedSender" readonly>
                    </div>
                    <div class="setting-item">
                        <label>Check Interval (minutes)</label>
                        <input type="number" id="checkInterval" min="1" max="60" value="5">
                    </div>
                </div>

                <div class="settings-card">
                    <h3><i class="fas fa-robot"></i> AI Configuration</h3>
                    <div class="setting-item">
                        <label>AI Model</label>
                        <select id="aiModel">
                            <option value="gpt-4">GPT-4</option>
                            <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
                        </select>
                    </div>
                    <div class="setting-item">
                        <label>AI Status</label>
                        <div class="status-badge" id="aiStatus">
                            <i class="fas fa-circle"></i> Connected
                        </div>
                    </div>
                </div>

                <div class="settings-card">
                    <h3><i class="fab fa-whatsapp"></i> WhatsApp Configuration</h3>
                    <div class="setting-item">
                        <label>Business Phone</label>
                        <input type="text" id="businessPhone" readonly>
                    </div>
                    <div class="setting-item">
                        <label>API Status</label>
                        <div class="status-badge" id="whatsappStatus">
                            <i class="fas fa-circle"></i> Connected
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Add/Edit Employee Modal -->
    <div id="employeeModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle"><i class="fas fa-user-plus"></i> Add Employee</h3>
                <button class="modal-close" onclick="closeEmployeeModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="employeeForm" class="modal-body">
                <input type="hidden" id="employeeId">
                <div class="form-group">
                    <label for="employeeName">Employee Name</label>
                    <input type="text" id="employeeName" required placeholder="Enter employee name">
                </div>
                <div class="form-group">
                    <label for="employeePhone">WhatsApp Number</label>
                    <input type="tel" id="employeePhone" required placeholder="+1234567890">
                    <small>Include country code (e.g., +91 for India)</small>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn-secondary" onclick="closeEmployeeModal()">Cancel</button>
                    <button type="submit" class="btn-primary">
                        <i class="fas fa-save"></i> Save Employee
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Email Detail Modal -->
    <div id="emailModal" class="modal">
        <div class="modal-content large">
            <div class="modal-header">
                <h3><i class="fas fa-envelope-open"></i> Email Details</h3>
                <button class="modal-close" onclick="closeEmailModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body" id="emailModalContent">
                <!-- Email details will be loaded here -->
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <p>Loading...</p>
        </div>
    </div>

    <!-- Toast Notifications -->
    <div id="toastContainer" class="toast-container"></div>

    <!-- Scripts -->
    <script src="js/app.js"></script>
    <script src="js/dashboard.js"></script>
    <script src="js/emails.js"></script>
    <script src="js/employees.js"></script>
    <script src="js/settings.js"></script>

    <script>
        // Enhanced Dashboard Functions
        function switchTab(tabName) {
            // Remove active class from all tabs
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });

            // Remove active class from all nav links
            document.querySelectorAll('.nav-link').forEach(link => {
                link.classList.remove('active');
            });

            // Show the selected tab
            const targetTab = document.getElementById(tabName);
            if (targetTab) {
                targetTab.classList.add('active');
            }

            // Activate the corresponding nav link
            const targetNavLink = document.querySelector(`[data-tab="${tabName}"]`);
            if (targetNavLink) {
                targetNavLink.classList.add('active');
            }
        }

        function refreshDashboard() {
            // Show loading state for banner button
            const refreshBtn = document.querySelector('.banner-actions .btn-primary');
            if (refreshBtn) {
                const originalText = refreshBtn.innerHTML;
                refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Refreshing...';
                refreshBtn.disabled = true;

                // Simulate data refresh with animation
                setTimeout(() => {
                    // Update statistics with new data
                    updateDashboardStats();

                    // Reset button
                    refreshBtn.innerHTML = originalText;
                    refreshBtn.disabled = false;

                    // Add refresh animation to cards
                    const cards = document.querySelectorAll('.enhanced-stat-card');
                    cards.forEach((card, index) => {
                        card.style.animation = 'none';
                        setTimeout(() => {
                            card.style.animation = `fadeInUp 0.6s ease forwards`;
                            card.style.animationDelay = `${index * 0.1}s`;
                        }, 50);
                    });

                    // Show success message
                    showToast('success', 'Dashboard Refreshed', 'All statistics have been updated successfully');
                }, 2000);
            } else {
                // Fallback for other refresh buttons
                const refreshButtons = document.querySelectorAll('.btn-refresh, .action-card[onclick*="refreshDashboard"]');
                refreshButtons.forEach(btn => {
                    const icon = btn.querySelector('i');
                    if (icon) {
                        icon.classList.add('fa-spin');
                    }
                });

                setTimeout(() => {
                    refreshButtons.forEach(btn => {
                        const icon = btn.querySelector('i');
                        if (icon) {
                            icon.classList.remove('fa-spin');
                        }
                    });
                    showToast('success', 'Dashboard Refreshed', 'All data has been updated successfully');
                }, 1500);
            }
        }

        // Function to update dashboard statistics
        function updateDashboardStats() {
            // Simulate real-time data updates
            const stats = {
                emails: Math.floor(Math.random() * 5) + 13,
                notifications: Math.floor(Math.random() * 3) + 10,
                replies: Math.floor(Math.random() * 3) + 6,
                employees: 3
            };

            // Update numbers with animation
            animateNumber('totalEmails', stats.emails);
            animateNumber('totalNotifications', stats.notifications);
            animateNumber('totalReplies', stats.replies);
            animateNumber('totalEmployees', stats.employees);
        }

        // Function to animate number changes
        function animateNumber(elementId, targetValue) {
            const element = document.getElementById(elementId);
            if (!element) return;

            const currentValue = parseInt(element.textContent);
            if (currentValue === targetValue) return;

            const increment = targetValue > currentValue ? 1 : -1;
            const duration = 1000;
            const steps = Math.abs(targetValue - currentValue);
            const stepDuration = duration / steps;

            let current = currentValue;
            const timer = setInterval(() => {
                current += increment;
                element.textContent = current;

                if (current === targetValue) {
                    clearInterval(timer);
                }
            }, stepDuration);
        }

        function showToast(type, title, message) {
            // Simple toast notification
            const toast = document.createElement('div');
            toast.className = `toast ${type}`;
            toast.innerHTML = `
                <div class="toast-header">
                    <strong>${title}</strong>
                </div>
                <div class="toast-body">${message}</div>
            `;

            const container = document.getElementById('toastContainer') || document.body;
            container.appendChild(toast);

            // Auto remove after 3 seconds
            setTimeout(() => {
                toast.remove();
            }, 3000);
        }
    </script>
</body>
</html>
