<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>Email Monitor Agent Dashboard</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet">
    <link rel="stylesheet" href="css/styles.css">
    <style>
        /* Enhanced Rich UI/UX Styles */
        :root {
            --glass-bg: rgba(255, 255, 255, 0.1);
            --glass-border: rgba(255, 255, 255, 0.2);
            --shadow-glow: 0 8px 32px rgba(244, 124, 32, 0.15);
            --shadow-deep: 0 20px 40px rgba(0, 0, 0, 0.1);
            --gradient-primary: linear-gradient(135deg, #F47C20 0%, #ff8c42 100%);
            --gradient-secondary: linear-gradient(135deg, #0B2A5A 0%, #1a4480 100%);
            --gradient-card: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
        }

        /* Premium Typography */
        body {
            font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif !important;
            font-weight: 400;
            letter-spacing: -0.01em;
        }

        h1, h2, h3, h4, h5, h6 {
            font-family: 'Poppins', sans-serif !important;
            font-weight: 600;
            letter-spacing: -0.02em;
        }

        .monospace {
            font-family: 'JetBrains Mono', monospace !important;
        }

        /* Enhanced Navigation */
        .navbar {
            background: var(--gradient-secondary) !important;
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 8px 32px rgba(11, 42, 90, 0.2);
        }

        .nav-brand {
            font-weight: 700 !important;
            font-size: 1.4rem !important;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .nav-brand i {
            color: #F47C20 !important;
            filter: drop-shadow(0 2px 4px rgba(244, 124, 32, 0.3));
            animation: pulse-glow 3s ease-in-out infinite;
        }

        @keyframes pulse-glow {
            0%, 100% { filter: drop-shadow(0 2px 4px rgba(244, 124, 32, 0.3)); }
            50% { filter: drop-shadow(0 2px 8px rgba(244, 124, 32, 0.6)); }
        }

        .nav-link {
            font-weight: 500 !important;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
            position: relative;
            overflow: hidden;
        }

        .nav-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transition: left 0.5s;
        }

        .nav-link:hover::before {
            left: 100%;
        }

        .nav-link:hover, .nav-link.active {
            background: var(--gradient-primary) !important;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(244, 124, 32, 0.3);
        }

        /* Enhanced Buttons */
        .btn-primary {
            background: var(--gradient-primary) !important;
            border: none !important;
            font-weight: 600 !important;
            letter-spacing: 0.025em !important;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
            position: relative;
            overflow: hidden;
        }

        .btn-primary::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .btn-primary:hover::before {
            left: 100%;
        }

        .btn-primary:hover {
            transform: translateY(-2px) !important;
            box-shadow: var(--shadow-glow) !important;
        }

        /* Enhanced Cards */
        .stat-card {
            background: var(--gradient-card) !important;
            border: 1px solid rgba(244, 124, 32, 0.1) !important;
            border-left: 4px solid #F47C20 !important;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
            position: relative;
            overflow: hidden;
        }

        .stat-card::after {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 100px;
            height: 100px;
            background: radial-gradient(circle, rgba(244, 124, 32, 0.05) 0%, transparent 70%);
            transform: translate(30px, -30px);
        }

        .stat-card:hover {
            transform: translateY(-4px) !important;
            box-shadow: var(--shadow-deep) !important;
            border-color: rgba(244, 124, 32, 0.3) !important;
        }

        /* Enhanced Welcome Message */
        .welcome-message {
            background: var(--gradient-primary) !important;
            color: #FFFFFF !important;
            border: none !important;
            box-shadow: var(--shadow-glow) !important;
            position: relative;
            overflow: hidden;
        }

        .welcome-message::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translate(-50%, -50%) rotate(0deg); }
            50% { transform: translate(-50%, -50%) rotate(180deg); }
        }

        .welcome-message h2 {
            font-weight: 700 !important;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        /* Enhanced Card Headers */
        .card-header {
            background: var(--gradient-secondary) !important;
            color: white !important;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
        }

        .card-header h3 {
            font-weight: 600 !important;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .card-header h3 i {
            color: #F47C20 !important;
            filter: drop-shadow(0 1px 2px rgba(244, 124, 32, 0.3));
        }

        /* Enhanced Page Headers */
        .page-header h1 {
            font-weight: 700 !important;
            font-size: 2.5rem !important;
            background: linear-gradient(135deg, #2E2E2E 0%, #4A5568 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .page-header h1 i {
            color: #F47C20 !important;
            filter: drop-shadow(0 2px 4px rgba(244, 124, 32, 0.3));
            -webkit-text-fill-color: #F47C20;
        }

        /* Enhanced Animations */
        .animate-fade-in {
            animation: fadeInUp 0.6s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Enhanced Status Indicator */
        .status-indicator {
            background: rgba(255, 255, 255, 0.15) !important;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            font-weight: 500;
        }

        /* Enhanced Scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        ::-webkit-scrollbar-track {
            background: rgba(244, 124, 32, 0.05);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #F47C20, #ff8c42);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #e06b1a, #F47C20);
        }

        /* Enhanced Page Header */
        .page-header {
            display: flex !important;
            justify-content: space-between !important;
            align-items: flex-start !important;
            margin-bottom: 2rem !important;
        }

        .page-header-actions {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        /* Enhanced Welcome Message */
        .welcome-content {
            position: relative;
            z-index: 2;
        }

        .welcome-features {
            display: flex;
            gap: 1rem;
            margin-top: 1.5rem;
            flex-wrap: wrap;
        }

        .feature-badge {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 25px;
            font-size: 0.875rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }

        .feature-badge:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        /* Enhanced Stat Cards */
        .stat-trend {
            font-size: 0.75rem;
            color: #6E7C8E;
            margin-top: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.25rem;
            font-weight: 500;
        }

        .stat-trend i {
            color: #10b981;
        }

        .stat-content h3 {
            font-size: 2.5rem !important;
            font-weight: 800 !important;
            margin-bottom: 0.5rem !important;
        }

        .stat-content p {
            font-weight: 600 !important;
            color: #4A5568 !important;
            margin-bottom: 0.75rem !important;
        }

        /* Enhanced Dashboard Cards */
        .dashboard-card {
            background: var(--gradient-card) !important;
            border: 1px solid rgba(244, 124, 32, 0.08) !important;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
        }

        .dashboard-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 28px rgba(0, 0, 0, 0.12);
        }

        /* Enhanced Form Elements */
        .filter-select, .filter-input {
            font-family: 'Inter', sans-serif !important;
            font-weight: 500 !important;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
        }

        .filter-select:focus, .filter-input:focus {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(244, 124, 32, 0.15) !important;
        }

        /* Enhanced Modal */
        .modal-content {
            background: var(--gradient-card) !important;
            border: 1px solid rgba(244, 124, 32, 0.1) !important;
        }

        /* Enhanced Loading */
        .loading-spinner {
            background: rgba(255, 255, 255, 0.95);
            padding: 2rem;
            border-radius: 1rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        /* Enhanced Toast */
        .toast {
            background: var(--gradient-card) !important;
            border: 1px solid rgba(244, 124, 32, 0.1) !important;
            backdrop-filter: blur(20px);
        }

        /* Responsive Enhancements */
        @media (max-width: 768px) {
            .page-header {
                flex-direction: column !important;
                align-items: flex-start !important;
                gap: 1rem !important;
            }

            .welcome-features {
                justify-content: center;
            }

            .feature-badge {
                font-size: 0.75rem;
                padding: 0.375rem 0.75rem;
            }

            .stat-content h3 {
                font-size: 2rem !important;
            }
        }

        /* Status Summary */
        .status-summary {
            margin-top: 1.5rem;
            padding-top: 1rem;
            border-top: 1px solid rgba(244, 124, 32, 0.1);
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }

        .summary-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
        }

        .summary-label {
            font-size: 0.75rem;
            color: #6E7C8E;
            font-weight: 500;
            margin-bottom: 0.25rem;
        }

        .summary-value {
            font-size: 1.25rem;
            font-weight: 700;
            color: #F47C20;
        }

        /* Enhanced Employee Cards */
        .employees-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 1.5rem;
            padding: 0;
        }

        .employee-card {
            background: var(--gradient-card) !important;
            border: 1px solid rgba(244, 124, 32, 0.1) !important;
            border-radius: 16px !important;
            padding: 1.5rem !important;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
            position: relative;
            overflow: hidden;
        }

        .employee-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient-primary);
        }

        .employee-card:hover {
            transform: translateY(-4px) !important;
            box-shadow: 0 12px 28px rgba(0, 0, 0, 0.15) !important;
            border-color: rgba(244, 124, 32, 0.3) !important;
        }

        .employee-header {
            display: flex !important;
            justify-content: space-between !important;
            align-items: flex-start !important;
            margin-bottom: 1.5rem !important;
        }

        .employee-avatar {
            width: 60px !important;
            height: 60px !important;
            border-radius: 50% !important;
            background: var(--gradient-primary) !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            color: white !important;
            font-size: 1.5rem !important;
            font-weight: 700 !important;
            font-family: 'Poppins', sans-serif !important;
            box-shadow: 0 4px 12px rgba(244, 124, 32, 0.3);
        }

        .employee-actions {
            display: flex !important;
            gap: 0.5rem !important;
        }

        .btn-icon {
            width: 36px !important;
            height: 36px !important;
            border: none !important;
            border-radius: 8px !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            cursor: pointer !important;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
            font-size: 0.875rem !important;
        }

        .btn-icon.edit {
            background: rgba(244, 124, 32, 0.1) !important;
            color: #F47C20 !important;
        }

        .btn-icon.edit:hover {
            background: rgba(244, 124, 32, 0.2) !important;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(244, 124, 32, 0.2);
        }

        .btn-icon.delete {
            background: rgba(239, 68, 68, 0.1) !important;
            color: #ef4444 !important;
        }

        .btn-icon.delete:hover {
            background: rgba(239, 68, 68, 0.2) !important;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(239, 68, 68, 0.2);
        }

        .employee-info h3 {
            font-size: 1.25rem !important;
            font-weight: 600 !important;
            color: #2E2E2E !important;
            margin-bottom: 1rem !important;
            font-family: 'Poppins', sans-serif !important;
        }

        .employee-phone, .employee-email {
            display: flex !important;
            align-items: center !important;
            gap: 0.75rem !important;
            margin-bottom: 0.75rem !important;
            color: #6E7C8E !important;
            font-size: 0.875rem !important;
            font-weight: 500 !important;
        }

        .employee-phone i {
            color: #25d366 !important;
            font-size: 1rem !important;
        }

        .employee-email i {
            color: #F47C20 !important;
            font-size: 1rem !important;
        }

        .employee-status {
            margin-top: 1.5rem !important;
            padding-top: 1.5rem !important;
            border-top: 1px solid rgba(244, 124, 32, 0.1) !important;
        }

        .status-badge {
            display: inline-flex !important;
            align-items: center !important;
            gap: 0.5rem !important;
            padding: 0.5rem 1rem !important;
            border-radius: 20px !important;
            font-size: 0.75rem !important;
            font-weight: 600 !important;
            margin-bottom: 1rem !important;
        }

        .status-badge.active {
            background: rgba(16, 185, 129, 0.1) !important;
            color: #10b981 !important;
            border: 1px solid rgba(16, 185, 129, 0.2);
        }

        .status-badge.inactive {
            background: rgba(100, 116, 139, 0.1) !important;
            color: #64748b !important;
            border: 1px solid rgba(100, 116, 139, 0.2);
        }

        .employee-stats {
            display: grid !important;
            grid-template-columns: 1fr 1fr !important;
            gap: 1rem !important;
        }

        .stat-item {
            display: flex !important;
            flex-direction: column !important;
            align-items: center !important;
            text-align: center !important;
        }

        .stat-label {
            font-size: 0.75rem !important;
            color: #6E7C8E !important;
            font-weight: 500 !important;
            margin-bottom: 0.25rem !important;
        }

        .stat-value {
            font-size: 1rem !important;
            font-weight: 600 !important;
            color: #2E2E2E !important;
        }

        /* Responsive Employee Cards */
        @media (max-width: 768px) {
            .employees-list {
                grid-template-columns: 1fr !important;
                gap: 1rem !important;
            }

            .employee-card {
                padding: 1rem !important;
            }

            .employee-avatar {
                width: 50px !important;
                height: 50px !important;
                font-size: 1.25rem !important;
            }

            .btn-icon {
                width: 32px !important;
                height: 32px !important;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <i class="fas fa-envelope-open-text"></i>
                <span>Email Monitor Agent</span>
            </div>
            <div class="nav-menu">
                <a href="#dashboard" class="nav-link active" data-tab="dashboard">
                    <i class="fas fa-chart-line"></i> Dashboard
                </a>
                <a href="#emails" class="nav-link" data-tab="emails">
                    <i class="fas fa-inbox"></i> Email Logs
                </a>
                <a href="#employees" class="nav-link" data-tab="employees">
                    <i class="fas fa-users"></i> Employees
                </a>
                <a href="#settings" class="nav-link" data-tab="settings">
                    <i class="fas fa-cog"></i> Settings
                </a>
            </div>
            <div class="nav-status">
                <div class="status-indicator" id="agentStatus">
                    <i class="fas fa-circle"></i>
                    <span>Agent Status</span>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Dashboard Tab -->
        <div id="dashboard" class="tab-content active animate-fade-in">
            <div class="page-header animate__animated animate__fadeInDown">
                <div>
                    <h1><i class="fas fa-chart-line"></i> Dashboard</h1>
                    <p>Monitor your email processing and system performance with real-time insights</p>
                </div>
                <div class="page-header-actions">
                    <button class="btn-primary" onclick="refreshDashboard()">
                        <i class="fas fa-sync-alt"></i> Refresh Data
                    </button>
                </div>
            </div>

            <!-- Welcome Message -->
            <div class="welcome-message animate__animated animate__fadeInUp">
                <div class="welcome-content">
                    <h2><i class="fas fa-rocket"></i> Welcome to Your Email Monitor Agent Dashboard!</h2>
                    <p>Your intelligent email monitoring system is actively running. Track performance, manage notifications, and optimize your workflow with powerful AI-driven insights.</p>
                    <div class="welcome-features">
                        <span class="feature-badge"><i class="fas fa-brain"></i> AI-Powered</span>
                        <span class="feature-badge"><i class="fas fa-shield-alt"></i> Secure</span>
                        <span class="feature-badge"><i class="fas fa-clock"></i> Real-time</span>
                    </div>
                </div>
            </div>

            <!-- Stats Cards -->
            <div class="stats-grid">
                <div class="stat-card animate__animated animate__fadeInUp" style="animation-delay: 0.1s">
                    <div class="stat-icon emails">
                        <i class="fas fa-envelope"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="totalEmails" class="monospace">15</h3>
                        <p>Total Emails Processed</p>
                        <span class="stat-change positive" id="emailsChange">+4 today</span>
                        <div class="stat-trend">
                            <i class="fas fa-arrow-up"></i> 12% increase
                        </div>
                    </div>
                </div>

                <div class="stat-card animate__animated animate__fadeInUp" style="animation-delay: 0.2s">
                    <div class="stat-icon whatsapp">
                        <i class="fab fa-whatsapp"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="totalNotifications" class="monospace">12</h3>
                        <p>WhatsApp Notifications</p>
                        <span class="stat-change positive" id="notificationsChange">+3 today</span>
                        <div class="stat-trend">
                            <i class="fas fa-arrow-up"></i> 8% increase
                        </div>
                    </div>
                </div>

                <div class="stat-card animate__animated animate__fadeInUp" style="animation-delay: 0.3s">
                    <div class="stat-icon replies">
                        <i class="fas fa-reply"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="totalReplies" class="monospace">8</h3>
                        <p>Auto Replies Sent</p>
                        <span class="stat-change positive" id="repliesChange">+2 today</span>
                        <div class="stat-trend">
                            <i class="fas fa-arrow-up"></i> 5% increase
                        </div>
                    </div>
                </div>

                <div class="stat-card animate__animated animate__fadeInUp" style="animation-delay: 0.4s">
                    <div class="stat-icon employees">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="totalEmployees" class="monospace">3</h3>
                        <p>Active Employees</p>
                        <span class="stat-change neutral" id="employeesChange">All Online</span>
                        <div class="stat-trend">
                            <i class="fas fa-check-circle"></i> 100% active
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="dashboard-grid animate__animated animate__fadeInUp" style="animation-delay: 0.5s">
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3><i class="fas fa-clock"></i> Recent Email Activity</h3>
                        <button class="btn-refresh" onclick="refreshDashboard()">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                    </div>
                    <div class="card-content">
                        <div id="recentEmails" class="activity-list">
                            <div class="activity-item">
                                <div class="activity-icon email">
                                    <i class="fas fa-envelope"></i>
                                </div>
                                <div class="activity-content">
                                    <div class="activity-title">New <NAME_EMAIL></div>
                                    <div class="activity-description">AI Summary: Customer inquiry about product pricing and availability</div>
                                </div>
                                <div class="activity-time">2 min ago</div>
                            </div>
                            <div class="activity-item">
                                <div class="activity-icon whatsapp">
                                    <i class="fab fa-whatsapp"></i>
                                </div>
                                <div class="activity-content">
                                    <div class="activity-title">WhatsApp notification sent</div>
                                    <div class="activity-description">Urgent email alert sent to team lead</div>
                                </div>
                                <div class="activity-time">5 min ago</div>
                            </div>
                            <div class="activity-item">
                                <div class="activity-icon reply">
                                    <i class="fas fa-reply"></i>
                                </div>
                                <div class="activity-content">
                                    <div class="activity-title">Auto-reply sent</div>
                                    <div class="activity-description">Acknowledgment sent to customer inquiry</div>
                                </div>
                                <div class="activity-time">8 min ago</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="dashboard-card">
                    <div class="card-header">
                        <h3><i class="fas fa-chart-pie"></i> Processing Status</h3>
                    </div>
                    <div class="card-content">
                        <div class="status-chart">
                            <div class="status-item">
                                <div class="status-bar">
                                    <div class="status-fill processed" style="width: 75%" id="processedBar"></div>
                                </div>
                                <div class="status-label">
                                    <span class="status-dot processed"></span>
                                    <span>Processed (<span id="processedCount" class="monospace">12</span>)</span>
                                </div>
                            </div>
                            <div class="status-item">
                                <div class="status-bar">
                                    <div class="status-fill pending" style="width: 20%" id="pendingBar"></div>
                                </div>
                                <div class="status-label">
                                    <span class="status-dot pending"></span>
                                    <span>Pending (<span id="pendingCount" class="monospace">2</span>)</span>
                                </div>
                            </div>
                            <div class="status-item">
                                <div class="status-bar">
                                    <div class="status-fill failed" style="width: 5%" id="failedBar"></div>
                                </div>
                                <div class="status-label">
                                    <span class="status-dot failed"></span>
                                    <span>Failed (<span id="failedCount" class="monospace">1</span>)</span>
                                </div>
                            </div>
                        </div>
                        <div class="status-summary">
                            <div class="summary-item">
                                <span class="summary-label">Success Rate</span>
                                <span class="summary-value monospace">93.3%</span>
                            </div>
                            <div class="summary-item">
                                <span class="summary-label">Avg Response Time</span>
                                <span class="summary-value monospace">1.2s</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Email Logs Tab -->
        <div id="emails" class="tab-content">
            <div class="page-header">
                <h1><i class="fas fa-inbox"></i> Email Logs</h1>
                <p>View and manage processed emails with AI summaries</p>
            </div>

            <!-- Filters -->
            <div class="filters-section">
                <div class="filters-grid">
                    <div class="filter-group">
                        <label for="dateFilter">Date Range</label>
                        <select id="dateFilter" class="filter-select">
                            <option value="today">Today</option>
                            <option value="week">This Week</option>
                            <option value="month">This Month</option>
                            <option value="all">All Time</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label for="senderFilter">Sender</label>
                        <input type="text" id="senderFilter" class="filter-input" placeholder="Filter by sender...">
                    </div>
                    <div class="filter-group">
                        <label for="statusFilter">Status</label>
                        <select id="statusFilter" class="filter-select">
                            <option value="all">All Status</option>
                            <option value="processed">Processed</option>
                            <option value="pending">Pending</option>
                            <option value="failed">Failed</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <button class="btn-primary" onclick="applyFilters()">
                            <i class="fas fa-filter"></i> Apply Filters
                        </button>
                        <button class="btn-secondary" onclick="clearFilters()">
                            <i class="fas fa-times"></i> Clear
                        </button>
                    </div>
                </div>
            </div>

            <!-- Email List -->
            <div class="emails-container">
                <div id="emailsList" class="emails-list">
                    <!-- Email items will be loaded here -->
                </div>
                <div class="pagination" id="emailsPagination">
                    <!-- Pagination will be loaded here -->
                </div>
            </div>
        </div>

        <!-- Employees Tab -->
        <div id="employees" class="tab-content">
            <div class="page-header animate__animated animate__fadeInDown">
                <div>
                    <h1><i class="fas fa-users"></i> Employee Management</h1>
                    <p>Manage WhatsApp recipients for email notifications</p>
                </div>
                <div class="page-header-actions">
                    <button class="btn-primary" onclick="openAddEmployeeModal()">
                        <i class="fas fa-plus"></i> Add Employee
                    </button>
                </div>
            </div>

            <!-- Employees List -->
            <div class="employees-container animate__animated animate__fadeInUp">
                <div id="employeesList" class="employees-list">
                    <!-- Sample Employee Cards -->
                    <div class="employee-card animate__animated animate__fadeInUp" style="animation-delay: 0.1s">
                        <div class="employee-header">
                            <div class="employee-avatar">
                                <span>VG</span>
                            </div>
                            <div class="employee-actions">
                                <button class="btn-icon edit" onclick="editEmployee(1)">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn-icon delete" onclick="deleteEmployee(1)">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                        <div class="employee-info">
                            <h3>Vishnu Bala Guru</h3>
                            <div class="employee-phone">
                                <i class="fab fa-whatsapp"></i>
                                <span class="monospace">+917598638873</span>
                            </div>
                            <div class="employee-email">
                                <i class="fas fa-envelope"></i>
                                <span><EMAIL></span>
                            </div>
                        </div>
                        <div class="employee-status">
                            <div class="status-badge active">
                                <i class="fas fa-circle"></i> Active
                            </div>
                            <div class="employee-stats">
                                <div class="stat-item">
                                    <span class="stat-label">Notifications</span>
                                    <span class="stat-value monospace">24</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">Last Active</span>
                                    <span class="stat-value">2 min ago</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="employee-card animate__animated animate__fadeInUp" style="animation-delay: 0.2s">
                        <div class="employee-header">
                            <div class="employee-avatar">
                                <span>JD</span>
                            </div>
                            <div class="employee-actions">
                                <button class="btn-icon edit" onclick="editEmployee(2)">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn-icon delete" onclick="deleteEmployee(2)">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                        <div class="employee-info">
                            <h3>John Doe</h3>
                            <div class="employee-phone">
                                <i class="fab fa-whatsapp"></i>
                                <span class="monospace">+1234567890</span>
                            </div>
                            <div class="employee-email">
                                <i class="fas fa-envelope"></i>
                                <span><EMAIL></span>
                            </div>
                        </div>
                        <div class="employee-status">
                            <div class="status-badge active">
                                <i class="fas fa-circle"></i> Active
                            </div>
                            <div class="employee-stats">
                                <div class="stat-item">
                                    <span class="stat-label">Notifications</span>
                                    <span class="stat-value monospace">18</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">Last Active</span>
                                    <span class="stat-value">5 min ago</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="employee-card animate__animated animate__fadeInUp" style="animation-delay: 0.3s">
                        <div class="employee-header">
                            <div class="employee-avatar">
                                <span>SM</span>
                            </div>
                            <div class="employee-actions">
                                <button class="btn-icon edit" onclick="editEmployee(3)">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn-icon delete" onclick="deleteEmployee(3)">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                        <div class="employee-info">
                            <h3>Sarah Miller</h3>
                            <div class="employee-phone">
                                <i class="fab fa-whatsapp"></i>
                                <span class="monospace">+4412345678</span>
                            </div>
                            <div class="employee-email">
                                <i class="fas fa-envelope"></i>
                                <span><EMAIL></span>
                            </div>
                        </div>
                        <div class="employee-status">
                            <div class="status-badge inactive">
                                <i class="fas fa-circle"></i> Offline
                            </div>
                            <div class="employee-stats">
                                <div class="stat-item">
                                    <span class="stat-label">Notifications</span>
                                    <span class="stat-value monospace">7</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">Last Active</span>
                                    <span class="stat-value">2 hours ago</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Settings Tab -->
        <div id="settings" class="tab-content">
            <div class="page-header">
                <h1><i class="fas fa-cog"></i> Settings</h1>
                <p>Configure your Email Monitor Agent</p>
            </div>

            <div class="settings-grid">
                <div class="settings-card">
                    <h3><i class="fas fa-envelope"></i> Email Configuration</h3>
                    <div class="setting-item">
                        <label>Monitoring Email</label>
                        <input type="email" id="monitoringEmail" readonly>
                    </div>
                    <div class="setting-item">
                        <label>Allowed Sender</label>
                        <input type="email" id="allowedSender" readonly>
                    </div>
                    <div class="setting-item">
                        <label>Check Interval (minutes)</label>
                        <input type="number" id="checkInterval" min="1" max="60" value="5">
                    </div>
                </div>

                <div class="settings-card">
                    <h3><i class="fas fa-robot"></i> AI Configuration</h3>
                    <div class="setting-item">
                        <label>AI Model</label>
                        <select id="aiModel">
                            <option value="gpt-4">GPT-4</option>
                            <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
                        </select>
                    </div>
                    <div class="setting-item">
                        <label>AI Status</label>
                        <div class="status-badge" id="aiStatus">
                            <i class="fas fa-circle"></i> Connected
                        </div>
                    </div>
                </div>

                <div class="settings-card">
                    <h3><i class="fab fa-whatsapp"></i> WhatsApp Configuration</h3>
                    <div class="setting-item">
                        <label>Business Phone</label>
                        <input type="text" id="businessPhone" readonly>
                    </div>
                    <div class="setting-item">
                        <label>API Status</label>
                        <div class="status-badge" id="whatsappStatus">
                            <i class="fas fa-circle"></i> Connected
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Add/Edit Employee Modal -->
    <div id="employeeModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle"><i class="fas fa-user-plus"></i> Add Employee</h3>
                <button class="modal-close" onclick="closeEmployeeModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="employeeForm" class="modal-body">
                <input type="hidden" id="employeeId">
                <div class="form-group">
                    <label for="employeeName">Employee Name</label>
                    <input type="text" id="employeeName" required placeholder="Enter employee name">
                </div>
                <div class="form-group">
                    <label for="employeePhone">WhatsApp Number</label>
                    <input type="tel" id="employeePhone" required placeholder="+1234567890">
                    <small>Include country code (e.g., +91 for India)</small>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn-secondary" onclick="closeEmployeeModal()">Cancel</button>
                    <button type="submit" class="btn-primary">
                        <i class="fas fa-save"></i> Save Employee
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Email Detail Modal -->
    <div id="emailModal" class="modal">
        <div class="modal-content large">
            <div class="modal-header">
                <h3><i class="fas fa-envelope-open"></i> Email Details</h3>
                <button class="modal-close" onclick="closeEmailModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body" id="emailModalContent">
                <!-- Email details will be loaded here -->
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <p>Loading...</p>
        </div>
    </div>

    <!-- Toast Notifications -->
    <div id="toastContainer" class="toast-container"></div>

    <!-- Scripts -->
    <script src="js/app.js"></script>
    <script src="js/dashboard.js"></script>
    <script src="js/emails.js"></script>
    <script src="js/employees.js"></script>
    <script src="js/settings.js"></script>
</body>
</html>
