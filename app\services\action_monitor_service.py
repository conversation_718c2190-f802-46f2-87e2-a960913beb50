"""
Action monitoring service for tracking all system activities and events.
"""

import json
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timezone
from sqlalchemy.orm import Session
from ..models import ActionMonitor, EmailLog

logger = logging.getLogger(__name__)


class ActionMonitorService:
    """
    Service for monitoring and tracking all system actions and events.
    """
    
    def __init__(self, db_session: Session):
        """
        Initialize the action monitor service.
        
        Args:
            db_session: Database session
        """
        self.db_session = db_session
    
    def log_action(self, 
                   action_type: str,
                   action_category: str,
                   action_description: str,
                   email_log_id: Optional[int] = None,
                   target_recipient: Optional[str] = None,
                   employee_name: Optional[str] = None,
                   status: str = 'pending',
                   additional_data: Optional[Dict] = None,
                   user_agent: Optional[str] = None,
                   ip_address: Optional[str] = None) -> ActionMonitor:
        """
        Log a new action to the monitoring system.
        
        Args:
            action_type: Type of action (email_received, whatsapp_sent, voice_call, etc.)
            action_category: Category (notification, communication, processing, system)
            action_description: Human-readable description
            email_log_id: Optional link to email log
            target_recipient: Phone/email of recipient
            employee_name: Employee name if applicable
            status: Initial status (pending, in_progress, etc.)
            additional_data: Extra data as dictionary
            user_agent: User agent for web actions
            ip_address: IP address for web actions
            
        Returns:
            ActionMonitor: Created action monitor record
        """
        try:
            action_monitor = ActionMonitor(
                email_log_id=email_log_id,
                action_type=action_type,
                action_category=action_category,
                action_description=action_description,
                target_recipient=target_recipient,
                employee_name=employee_name,
                status=status,
                user_agent=user_agent,
                ip_address=ip_address,
                additional_data=json.dumps(additional_data) if additional_data else None
            )
            
            self.db_session.add(action_monitor)
            self.db_session.commit()
            
            logger.info(f"Logged action: {action_type} - {action_description}")
            return action_monitor
            
        except Exception as e:
            logger.error(f"Failed to log action {action_type}: {str(e)}")
            self.db_session.rollback()
            raise
    
    def complete_action(self, 
                       action_id: int,
                       success: bool,
                       error_message: Optional[str] = None,
                       response_data: Optional[Dict] = None,
                       cost: Optional[float] = None) -> bool:
        """
        Mark an action as completed.
        
        Args:
            action_id: ID of the action monitor record
            success: Whether the action was successful
            error_message: Error message if failed
            response_data: Response data from APIs
            cost: Cost of the action
            
        Returns:
            bool: True if successfully updated
        """
        try:
            action_monitor = self.db_session.query(ActionMonitor).filter(
                ActionMonitor.id == action_id
            ).first()
            
            if not action_monitor:
                logger.error(f"Action monitor {action_id} not found")
                return False
            
            action_monitor.mark_completed(
                success=success,
                error_message=error_message,
                response_data=json.dumps(response_data) if response_data else None,
                cost=cost
            )
            
            self.db_session.commit()
            
            logger.info(f"Completed action {action_id}: {'success' if success else 'failed'}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to complete action {action_id}: {str(e)}")
            self.db_session.rollback()
            return False
    
    def schedule_retry(self, action_id: int, delay_minutes: int = 10) -> bool:
        """
        Schedule an action for retry.
        
        Args:
            action_id: ID of the action monitor record
            delay_minutes: Minutes to wait before retry
            
        Returns:
            bool: True if successfully scheduled
        """
        try:
            action_monitor = self.db_session.query(ActionMonitor).filter(
                ActionMonitor.id == action_id
            ).first()
            
            if not action_monitor:
                logger.error(f"Action monitor {action_id} not found")
                return False
            
            action_monitor.schedule_retry(delay_minutes)
            self.db_session.commit()
            
            logger.info(f"Scheduled retry for action {action_id} in {delay_minutes} minutes")
            return True
            
        except Exception as e:
            logger.error(f"Failed to schedule retry for action {action_id}: {str(e)}")
            self.db_session.rollback()
            return False
    
    def get_actions(self, 
                   action_type: Optional[str] = None,
                   action_category: Optional[str] = None,
                   status: Optional[str] = None,
                   email_log_id: Optional[int] = None,
                   limit: int = 100,
                   offset: int = 0) -> List[ActionMonitor]:
        """
        Get actions with optional filtering.
        
        Args:
            action_type: Filter by action type
            action_category: Filter by category
            status: Filter by status
            email_log_id: Filter by email log ID
            limit: Maximum number of results
            offset: Offset for pagination
            
        Returns:
            List[ActionMonitor]: List of action monitor records
        """
        try:
            query = self.db_session.query(ActionMonitor)
            
            if action_type:
                query = query.filter(ActionMonitor.action_type == action_type)
            if action_category:
                query = query.filter(ActionMonitor.action_category == action_category)
            if status:
                query = query.filter(ActionMonitor.status == status)
            if email_log_id:
                query = query.filter(ActionMonitor.email_log_id == email_log_id)
            
            actions = query.order_by(ActionMonitor.initiated_at.desc()).offset(offset).limit(limit).all()
            return actions
            
        except Exception as e:
            logger.error(f"Failed to get actions: {str(e)}")
            return []
    
    def get_action_statistics(self, hours: int = 24) -> Dict:
        """
        Get action statistics for the specified time period.
        
        Args:
            hours: Number of hours to look back
            
        Returns:
            Dict: Statistics summary
        """
        try:
            from sqlalchemy import func
            from datetime import timedelta
            
            cutoff_time = datetime.now(timezone.utc) - timedelta(hours=hours)
            
            # Total actions
            total_actions = self.db_session.query(ActionMonitor).filter(
                ActionMonitor.initiated_at >= cutoff_time
            ).count()
            
            # Success rate
            successful_actions = self.db_session.query(ActionMonitor).filter(
                ActionMonitor.initiated_at >= cutoff_time,
                ActionMonitor.success == 'true'
            ).count()
            
            # Actions by type
            actions_by_type = self.db_session.query(
                ActionMonitor.action_type,
                func.count(ActionMonitor.id).label('count')
            ).filter(
                ActionMonitor.initiated_at >= cutoff_time
            ).group_by(ActionMonitor.action_type).all()
            
            # Actions by status
            actions_by_status = self.db_session.query(
                ActionMonitor.status,
                func.count(ActionMonitor.id).label('count')
            ).filter(
                ActionMonitor.initiated_at >= cutoff_time
            ).group_by(ActionMonitor.status).all()
            
            # Total cost
            total_cost = self.db_session.query(
                func.sum(ActionMonitor.cost)
            ).filter(
                ActionMonitor.initiated_at >= cutoff_time,
                ActionMonitor.cost.isnot(None)
            ).scalar() or 0.0
            
            # Average duration
            avg_duration = self.db_session.query(
                func.avg(ActionMonitor.duration_seconds)
            ).filter(
                ActionMonitor.initiated_at >= cutoff_time,
                ActionMonitor.duration_seconds.isnot(None)
            ).scalar() or 0.0
            
            success_rate = (successful_actions / total_actions * 100) if total_actions > 0 else 0
            
            return {
                'period_hours': hours,
                'total_actions': total_actions,
                'successful_actions': successful_actions,
                'success_rate': round(success_rate, 2),
                'total_cost': round(total_cost, 4),
                'average_duration_seconds': round(avg_duration, 2),
                'actions_by_type': {row.action_type: row.count for row in actions_by_type},
                'actions_by_status': {row.status: row.count for row in actions_by_status}
            }
            
        except Exception as e:
            logger.error(f"Failed to get action statistics: {str(e)}")
            return {}
    
    def get_pending_retries(self) -> List[ActionMonitor]:
        """
        Get actions that are pending retry.
        
        Returns:
            List[ActionMonitor]: Actions ready for retry
        """
        try:
            now = datetime.now(timezone.utc)
            
            pending_retries = self.db_session.query(ActionMonitor).filter(
                ActionMonitor.status == 'pending_retry',
                ActionMonitor.next_retry_at <= now,
                ActionMonitor.retry_count < 3  # Max 3 retries
            ).all()
            
            return pending_retries
            
        except Exception as e:
            logger.error(f"Failed to get pending retries: {str(e)}")
            return []


# Action type constants
class ActionTypes:
    EMAIL_RECEIVED = "email_received"
    EMAIL_PROCESSED = "email_processed"
    WHATSAPP_SENT = "whatsapp_sent"
    VOICE_CALL = "voice_call"
    EMAIL_REPLY = "email_reply"
    EMPLOYEE_ADDED = "employee_added"
    EMPLOYEE_UPDATED = "employee_updated"
    EMPLOYEE_DELETED = "employee_deleted"
    SYSTEM_STARTUP = "system_startup"
    SYSTEM_SHUTDOWN = "system_shutdown"
    API_REQUEST = "api_request"
    BACKGROUND_TASK = "background_task"


# Action category constants
class ActionCategories:
    NOTIFICATION = "notification"
    COMMUNICATION = "communication"
    PROCESSING = "processing"
    SYSTEM = "system"
    MANAGEMENT = "management"
    API = "api"


def get_action_monitor_service(db_session: Session) -> ActionMonitorService:
    """
    Factory function to get the action monitor service.
    
    Args:
        db_session: Database session
        
    Returns:
        ActionMonitorService: Action monitor service instance
    """
    return ActionMonitorService(db_session)
