import os
from dotenv import load_dotenv
from fastapi import FastAPI, Depends, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from sqlalchemy.orm import Session
import logging
import time
from typing import Dict

from .models import get_db
from .utils.logging_utils import setup_logging, RequestLoggingMiddleware, Error<PERSON>and<PERSON>
from .services.email_monitor import EmailMonitor, EmailProcessor, monitor_emails
from .services.ai_summarizer import get_ai_summarizer
from .services.whatsapp_notifier import get_whatsapp_notifier
from .services.email_replier import get_email_replier

# Load environment variables
load_dotenv()

# Set up logging
logger = setup_logging("email_monitor", "logs")
error_handler = ErrorHandler(logger)

# Create FastAPI app
app = FastAPI(
    title="Email Monitor Agent",
    description="AI-powered email monitoring, summarization, and notification system",
    version="1.0.0"
)

# Add middleware
app.add_middleware(RequestLoggingMiddleware, logger=logger)
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, replace with specific origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Configuration
def get_config():
    """Get application configuration from environment variables"""
    return {
        # Email monitoring (IMAP)
        "imap_host": os.getenv("IMAP_HOST"),
        "imap_port": int(os.getenv("IMAP_PORT", "993")),
        "imap_username": os.getenv("IMAP_USERNAME"),
        "imap_password": os.getenv("IMAP_PASSWORD"),
        "use_ssl": os.getenv("IMAP_USE_SSL", "True").lower() == "true",
        "mailbox": os.getenv("IMAP_MAILBOX", "INBOX"),
        "allowed_sender_email": os.getenv("ALLOWED_SENDER_EMAIL"),

        # AI summarization
        "use_openai": os.getenv("USE_OPENAI", "True").lower() == "true",
        "openai_api_key": os.getenv("OPENAI_API_KEY"),
        "openai_model": os.getenv("OPENAI_MODEL", "gpt-4"),
        "local_model_path": os.getenv("LOCAL_MODEL_PATH", "models/llama-7b"),

        # WhatsApp notification (Meta Cloud API)
        "mock_whatsapp": os.getenv("MOCK_WHATSAPP", "False").lower() == "true",
        "meta_api_token": os.getenv("META_API_TOKEN"),
        "meta_phone_number_id": os.getenv("META_PHONE_NUMBER_ID"),
        "team_numbers": os.getenv("TEAM_NUMBERS", "").split(","),

        # Email reply (SMTP)
        "smtp_host": os.getenv("SMTP_HOST"),
        "smtp_port": int(os.getenv("SMTP_PORT", "587")),
        "smtp_username": os.getenv("SMTP_USERNAME"),
        "smtp_password": os.getenv("SMTP_PASSWORD"),
        "smtp_use_ssl": os.getenv("SMTP_USE_SSL", "False").lower() == "true",
        "default_sender": os.getenv("DEFAULT_SENDER"),
        "mock_email": os.getenv("MOCK_EMAIL", "False").lower() == "true",

        # Polling interval
        "polling_interval": int(os.getenv("POLLING_INTERVAL", "300")),  # 5 minutes
    }

# Background task for email monitoring
def background_email_monitor(db: Session):
    """Background task for monitoring emails"""
    config = get_config()

    while True:
        try:
            logger.info("Starting email monitoring cycle")
            monitor_emails(config, db)
            logger.info(f"Email monitoring cycle completed, sleeping for {config['polling_interval']} seconds")
            time.sleep(config["polling_interval"])
        except Exception as e:
            error_handler.handle_error(e, "background_email_monitor")
            # Sleep before retrying to avoid rapid failure loops
            time.sleep(60)

# Process a single email through the entire pipeline
def process_email_pipeline(email_log_id: int, db: Session):
    """
    Process a single email through the entire pipeline:
    1. AI summarization
    2. WhatsApp notification
    3. Email reply
    """
    config = get_config()

    try:
        # Get email log
        from .models import EmailLog
        email_log = db.query(EmailLog).filter(EmailLog.id == email_log_id).first()

        if not email_log:
            logger.error(f"Email log {email_log_id} not found")
            return

        # Get email content
        # In a real implementation, this would retrieve the content from the database
        # or re-fetch from the email server if needed
        email_content = "Sample email content for testing"

        # AI summarization
        logger.info(f"Processing email {email_log_id} with AI")
        ai_summarizer = get_ai_summarizer(config)
        ai_result = ai_summarizer.process_email(email_log, email_content, db)

        if not ai_result.get('success', False):
            logger.error(f"AI processing failed for email {email_log_id}")
            return

        # WhatsApp notification using Meta Cloud API
        logger.info(f"Sending WhatsApp notifications for email {email_log_id} via Meta Cloud API")
        whatsapp_notifier = get_whatsapp_notifier(config)
        notifications = whatsapp_notifier.send_notification(email_log, db)

        # Email reply
        logger.info(f"Sending email reply for email {email_log_id}")
        email_replier = get_email_replier(config)
        reply = email_replier.send_reply(email_log, db)

        logger.info(f"Email {email_log_id} processed successfully")
    except Exception as e:
        error_handler.handle_error(e, f"process_email_pipeline for email {email_log_id}")

# API routes
@app.get("/api")
def read_api_root():
    """API root endpoint"""
    return {"message": "Email Monitor Agent API"}

@app.post("/api/monitor/start")
def start_monitoring(background_tasks: BackgroundTasks, db: Session = Depends(get_db)):
    """Start email monitoring in the background"""
    try:
        background_tasks.add_task(background_email_monitor, db)
        logger.info("Email monitoring started in background")
        return {"message": "Email monitoring started in background"}
    except Exception as e:
        error_info = error_handler.handle_error(e, "start_monitoring")
        return {"error": str(e), "details": error_info}

@app.post("/api/emails/{email_id}/process")
def process_email(email_id: int, background_tasks: BackgroundTasks, db: Session = Depends(get_db)):
    """Process a specific email through the pipeline"""
    try:
        background_tasks.add_task(process_email_pipeline, email_id, db)
        logger.info(f"Email {email_id} queued for processing")
        return {"message": f"Email {email_id} queued for processing"}
    except Exception as e:
        error_info = error_handler.handle_error(e, f"process_email {email_id}")
        return {"error": str(e), "details": error_info}

@app.post("/api/notifications/retry")
def retry_notifications(db: Session = Depends(get_db)):
    """Retry failed WhatsApp notifications"""
    try:
        config = get_config()
        whatsapp_notifier = get_whatsapp_notifier(config)
        success_count = whatsapp_notifier.retry_failed_notifications(max_retries=3, db_session=db)
        logger.info(f"Retried {success_count} WhatsApp notifications")
        return {"message": f"Retried {success_count} WhatsApp notifications"}
    except Exception as e:
        error_info = error_handler.handle_error(e, "retry_notifications")
        return {"error": str(e), "details": error_info}

@app.post("/api/replies/retry")
def retry_replies(db: Session = Depends(get_db)):
    """Retry failed email replies"""
    try:
        config = get_config()
        email_replier = get_email_replier(config)
        success_count = email_replier.retry_failed_replies(db_session=db, max_retries=3)
        logger.info(f"Retried {success_count} email replies")
        return {"message": f"Retried {success_count} email replies"}
    except Exception as e:
        error_info = error_handler.handle_error(e, "retry_replies")
        return {"error": str(e), "details": error_info}

# Employee Management (add directly to worker app)
from pydantic import BaseModel
from typing import List, Optional

class EmployeeCreate(BaseModel):
    name: str
    phone: str

class EmployeeUpdate(BaseModel):
    name: Optional[str] = None
    phone: Optional[str] = None

class EmployeeResponse(BaseModel):
    id: int
    name: str
    phone: str
    status: str = "active"
    created_at: str

# In-memory employee storage (for demo purposes)
employees_db = [
    {"id": 1, "name": "Vishnu Bala Guru", "phone": "+917598638873", "status": "active", "created_at": "2024-01-01T00:00:00"},
    {"id": 2, "name": "John Doe", "phone": "+1234567890", "status": "active", "created_at": "2024-01-02T00:00:00"},
]
next_employee_id = 3

@app.get("/api/employees", response_model=List[EmployeeResponse])
def get_employees():
    """Get all employees"""
    return employees_db

@app.post("/api/employees", response_model=EmployeeResponse)
def create_employee(employee: EmployeeCreate):
    """Create a new employee"""
    global next_employee_id

    from datetime import datetime

    new_employee = {
        "id": next_employee_id,
        "name": employee.name,
        "phone": employee.phone,
        "status": "active",
        "created_at": datetime.now().isoformat()
    }

    employees_db.append(new_employee)
    next_employee_id += 1

    return new_employee

@app.get("/api/employees/{employee_id}", response_model=EmployeeResponse)
def get_employee(employee_id: int):
    """Get a specific employee by ID"""
    employee = next((emp for emp in employees_db if emp["id"] == employee_id), None)
    if not employee:
        from fastapi import HTTPException
        raise HTTPException(status_code=404, detail="Employee not found")
    return employee

@app.put("/api/employees/{employee_id}", response_model=EmployeeResponse)
def update_employee(employee_id: int, employee: EmployeeUpdate):
    """Update an employee"""
    emp_index = next((i for i, emp in enumerate(employees_db) if emp["id"] == employee_id), None)
    if emp_index is None:
        from fastapi import HTTPException
        raise HTTPException(status_code=404, detail="Employee not found")

    if employee.name is not None:
        employees_db[emp_index]["name"] = employee.name
    if employee.phone is not None:
        employees_db[emp_index]["phone"] = employee.phone

    return employees_db[emp_index]

@app.delete("/api/employees/{employee_id}")
def delete_employee(employee_id: int):
    """Delete an employee"""
    global employees_db
    employees_db = [emp for emp in employees_db if emp["id"] != employee_id]
    return {"message": "Employee deleted successfully"}

# Email API endpoints (add directly to worker app)
from .models import EmailLog, WhatsAppNotification, EmailReply
from .schemas import EmailLogResponse, EmailLogDetail
from sqlalchemy import func

@app.get("/api/emails", response_model=List[EmailLogResponse])
def get_emails(
    status: Optional[str] = None,
    sender: Optional[str] = None,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    """Get all processed emails with optional filtering"""
    try:
        query = db.query(EmailLog)

        # Apply filters
        if status:
            query = query.filter(EmailLog.status == status)
        if sender:
            query = query.filter(EmailLog.sender.contains(sender))

        # Apply pagination and ordering
        emails = query.order_by(EmailLog.received_at.desc()).offset(skip).limit(limit).all()

        return emails
    except Exception as e:
        logger.error(f"Error getting emails: {str(e)}")
        from fastapi import HTTPException
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/emails/{email_id}", response_model=EmailLogDetail)
def get_email(email_id: int, db: Session = Depends(get_db)):
    """Get a specific email by ID"""
    try:
        email = db.query(EmailLog).filter(EmailLog.id == email_id).first()

        if not email:
            from fastapi import HTTPException
            raise HTTPException(status_code=404, detail=f"Email with ID {email_id} not found")

        return email
    except Exception as e:
        logger.error(f"Error getting email {email_id}: {str(e)}")
        from fastapi import HTTPException
        raise HTTPException(status_code=500, detail=str(e))

# Import other API routes (for any remaining endpoints)
from .main import app as api_app
# Don't mount the entire app, just use it for reference

# Frontend serving
import pathlib
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse

# Get the current directory
current_dir = pathlib.Path(__file__).parent.parent

# Serve frontend static files
try:
    frontend_dir = current_dir / "frontend"
    if frontend_dir.exists():
        app.mount("/static", StaticFiles(directory=str(frontend_dir)), name="static")
        logger.info(f"✅ Mounted static files from: {frontend_dir}")
    else:
        logger.warning(f"❌ Frontend directory not found: {frontend_dir}")
except Exception as e:
    logger.error(f"❌ Error mounting static files: {e}")

@app.get("/")
def serve_root():
    """Serve the main dashboard at root"""
    frontend_file = current_dir / "frontend" / "index.html"
    if frontend_file.exists():
        return FileResponse(str(frontend_file))
    else:
        logger.error(f"Frontend file not found: {frontend_file}")
        return {"error": "Frontend not found", "message": "Dashboard files are missing"}

@app.get("/dashboard")
def serve_dashboard():
    """Serve the dashboard frontend"""
    # Try the new simplified dashboard first
    dashboard_file = current_dir / "frontend" / "dashboard.html"
    if dashboard_file.exists():
        return FileResponse(str(dashboard_file))

    # Fallback to original
    frontend_file = current_dir / "frontend" / "index.html"
    if frontend_file.exists():
        return FileResponse(str(frontend_file))
    else:
        logger.error(f"Frontend file not found: {frontend_file}")
        return {"error": "Frontend not found", "message": "Dashboard files are missing"}

@app.get("/frontend")
def serve_frontend():
    """Serve the frontend"""
    dashboard_file = current_dir / "frontend" / "dashboard.html"
    if dashboard_file.exists():
        return FileResponse(str(dashboard_file))

    frontend_file = current_dir / "frontend" / "index.html"
    if frontend_file.exists():
        return FileResponse(str(frontend_file))
    else:
        logger.error(f"Frontend file not found: {frontend_file}")
        return {"error": "Frontend not found", "message": "Dashboard files are missing"}

# Serve individual static files as fallback
@app.get("/css/{file_path:path}")
def serve_css(file_path: str):
    """Serve CSS files"""
    from fastapi import HTTPException
    css_file = current_dir / "frontend" / "css" / file_path
    if css_file.exists():
        return FileResponse(str(css_file))
    else:
        logger.error(f"CSS file not found: {css_file}")
        raise HTTPException(status_code=404, detail=f"CSS file not found: {css_file}")

@app.get("/js/{file_path:path}")
def serve_js(file_path: str):
    """Serve JavaScript files"""
    from fastapi import HTTPException
    js_file = current_dir / "frontend" / "js" / file_path
    if js_file.exists():
        return FileResponse(str(js_file))
    else:
        logger.error(f"JS file not found: {js_file}")
        raise HTTPException(status_code=404, detail=f"JS file not found: {js_file}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("app.worker:app", host="0.0.0.0", port=8000, reload=True)
