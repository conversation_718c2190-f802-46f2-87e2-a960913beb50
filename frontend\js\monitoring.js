/**
 * Action Monitoring Dashboard
 * Handles the monitoring tab functionality for tracking system actions
 */

class ActionMonitoring {
    constructor() {
        this.currentPage = 1;
        this.pageSize = 20;
        this.filters = {
            action_type: '',
            status: ''
        };
        this.refreshInterval = null;
    }

    /**
     * Initialize the monitoring dashboard
     */
    async init() {
        console.log('Initializing Action Monitoring...');
        await this.loadStatistics();
        await this.loadRecentActions();
        this.setupEventListeners();
        this.startAutoRefresh();
    }

    /**
     * Load action statistics
     */
    async loadStatistics() {
        try {
            const response = await fetch('/api/actions/statistics?hours=24');
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const stats = await response.json();
            this.updateStatistics(stats);
            this.updateBreakdowns(stats);
        } catch (error) {
            console.error('Error loading statistics:', error);
            this.showError('Failed to load statistics');
        }
    }

    /**
     * Update statistics cards
     */
    updateStatistics(stats) {
        document.getElementById('successfulActions').textContent = stats.successful_actions || 0;
        document.getElementById('failedActions').textContent = (stats.total_actions || 0) - (stats.successful_actions || 0);
        document.getElementById('successRate').textContent = `${stats.success_rate || 0}%`;
        document.getElementById('totalCost').textContent = `$${(stats.total_cost || 0).toFixed(4)}`;
    }

    /**
     * Update breakdown charts
     */
    updateBreakdowns(stats) {
        // Actions by type
        const typeContainer = document.getElementById('actionsByType');
        typeContainer.innerHTML = '';
        
        if (stats.actions_by_type) {
            Object.entries(stats.actions_by_type).forEach(([type, count]) => {
                const item = document.createElement('div');
                item.className = 'breakdown-item';
                item.innerHTML = `
                    <div class="breakdown-label">${this.formatActionType(type)}</div>
                    <div class="breakdown-value">${count}</div>
                    <div class="breakdown-bar">
                        <div class="breakdown-fill" style="width: ${(count / stats.total_actions * 100)}%"></div>
                    </div>
                `;
                typeContainer.appendChild(item);
            });
        }

        // Actions by status
        const statusContainer = document.getElementById('actionsByStatus');
        statusContainer.innerHTML = '';
        
        if (stats.actions_by_status) {
            Object.entries(stats.actions_by_status).forEach(([status, count]) => {
                const item = document.createElement('div');
                item.className = 'breakdown-item';
                item.innerHTML = `
                    <div class="breakdown-label">${this.formatStatus(status)}</div>
                    <div class="breakdown-value">${count}</div>
                    <div class="breakdown-bar">
                        <div class="breakdown-fill ${this.getStatusClass(status)}" style="width: ${(count / stats.total_actions * 100)}%"></div>
                    </div>
                `;
                statusContainer.appendChild(item);
            });
        }
    }

    /**
     * Load recent actions
     */
    async loadRecentActions() {
        try {
            const params = new URLSearchParams({
                limit: this.pageSize,
                offset: (this.currentPage - 1) * this.pageSize,
                ...this.filters
            });

            const response = await fetch(`/api/actions?${params}`);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const actions = await response.json();
            this.updateActionsTable(actions);
        } catch (error) {
            console.error('Error loading recent actions:', error);
            this.showError('Failed to load recent actions');
        }
    }

    /**
     * Update actions table
     */
    updateActionsTable(actions) {
        const tbody = document.getElementById('actionsTableBody');
        tbody.innerHTML = '';

        if (!actions || actions.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="7" class="no-data">No actions found</td>
                </tr>
            `;
            return;
        }

        actions.forEach(action => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${this.formatDateTime(action.initiated_at)}</td>
                <td><span class="action-type-badge ${action.action_type}">${this.formatActionType(action.action_type)}</span></td>
                <td class="action-description">${action.action_description}</td>
                <td>${action.target_recipient || action.employee_name || '-'}</td>
                <td><span class="status-badge ${this.getStatusClass(action.status)}">${this.formatStatus(action.status)}</span></td>
                <td>${action.duration_seconds ? `${action.duration_seconds}s` : '-'}</td>
                <td>${action.cost ? `$${action.cost.toFixed(4)}` : '-'}</td>
            `;
            tbody.appendChild(row);
        });
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Filter changes
        document.getElementById('actionTypeFilter').addEventListener('change', (e) => {
            this.filters.action_type = e.target.value;
            this.currentPage = 1;
            this.loadRecentActions();
        });

        document.getElementById('actionStatusFilter').addEventListener('change', (e) => {
            this.filters.status = e.target.value;
            this.currentPage = 1;
            this.loadRecentActions();
        });
    }

    /**
     * Start auto-refresh
     */
    startAutoRefresh() {
        // Refresh every 30 seconds
        this.refreshInterval = setInterval(() => {
            this.loadStatistics();
            this.loadRecentActions();
        }, 30000);
    }

    /**
     * Stop auto-refresh
     */
    stopAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
    }

    /**
     * Manual refresh
     */
    async refresh() {
        await this.loadStatistics();
        await this.loadRecentActions();
    }

    /**
     * Format action type for display
     */
    formatActionType(type) {
        const types = {
            'email_received': 'Email Received',
            'email_processed': 'Email Processed',
            'whatsapp_sent': 'WhatsApp Sent',
            'voice_call': 'Voice Call',
            'email_reply': 'Email Reply',
            'employee_added': 'Employee Added',
            'employee_updated': 'Employee Updated',
            'employee_deleted': 'Employee Deleted',
            'system_startup': 'System Startup',
            'api_request': 'API Request',
            'background_task': 'Background Task'
        };
        return types[type] || type;
    }

    /**
     * Format status for display
     */
    formatStatus(status) {
        const statuses = {
            'pending': 'Pending',
            'in_progress': 'In Progress',
            'success': 'Success',
            'failed': 'Failed',
            'pending_retry': 'Pending Retry',
            'failed_permanently': 'Failed Permanently'
        };
        return statuses[status] || status;
    }

    /**
     * Get CSS class for status
     */
    getStatusClass(status) {
        const classes = {
            'pending': 'pending',
            'in_progress': 'in-progress',
            'success': 'success',
            'failed': 'failed',
            'pending_retry': 'warning',
            'failed_permanently': 'failed'
        };
        return classes[status] || 'default';
    }

    /**
     * Format date and time
     */
    formatDateTime(dateString) {
        if (!dateString) return '-';
        
        const date = new Date(dateString);
        const now = new Date();
        const diffMs = now - date;
        const diffMins = Math.floor(diffMs / 60000);
        const diffHours = Math.floor(diffMins / 60);
        const diffDays = Math.floor(diffHours / 24);

        if (diffMins < 1) return 'Just now';
        if (diffMins < 60) return `${diffMins}m ago`;
        if (diffHours < 24) return `${diffHours}h ago`;
        if (diffDays < 7) return `${diffDays}d ago`;
        
        return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
    }

    /**
     * Show error message
     */
    showError(message) {
        console.error(message);
        // You can implement a toast notification here
    }
}

// Global instance
let actionMonitoring = null;

/**
 * Initialize monitoring when tab is shown
 */
function initMonitoring() {
    if (!actionMonitoring) {
        actionMonitoring = new ActionMonitoring();
    }
    actionMonitoring.init();
}

/**
 * Refresh monitoring data
 */
function refreshMonitoring() {
    if (actionMonitoring) {
        actionMonitoring.refresh();
    }
}

/**
 * Cleanup when leaving monitoring tab
 */
function cleanupMonitoring() {
    if (actionMonitoring) {
        actionMonitoring.stopAutoRefresh();
    }
}
