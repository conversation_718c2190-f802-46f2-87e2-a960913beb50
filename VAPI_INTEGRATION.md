# VAPI Voice Calling Integration

This document explains how to set up and use the VAPI (Voice AI Platform) integration for making voice calls to employees when email notifications are received.

## 🎯 **Features**

### **Automatic Voice Calls**
- ✅ **Triggers voice calls** to employees when WhatsApp notifications are sent
- ✅ **Individual calls** to each active employee
- ✅ **Personalized messages** with employee names and email details
- ✅ **Automatic retry logic** for failed or unanswered calls

### **Smart Retry System**
- ✅ **10-minute delays** for rescheduling failed calls
- ✅ **Maximum 3 retries** per call to avoid spam
- ✅ **Background monitoring** for automatic retries
- ✅ **Permanent failure** marking after max retries

### **Call Status Tracking**
- ✅ **Real-time status** tracking (pending, initiated, ringing, answered, completed, failed)
- ✅ **Call duration** and cost tracking
- ✅ **Error logging** for troubleshooting
- ✅ **Database persistence** for all call records

## 🚀 **Setup Instructions**

### **1. VAPI Account Setup**
1. Sign up for a VAPI account at [https://vapi.ai](https://vapi.ai)
2. Create a new assistant for email notifications
3. Get your API key and assistant ID
4. (Optional) Set up a dedicated phone number for outbound calls

### **2. Environment Configuration**
Add the following variables to your `.env` file:

```env
# VAPI voice calling
VAPI_ENABLED=True
VAPI_API_KEY=your_vapi_api_key
VAPI_ASSISTANT_ID=your_vapi_assistant_id
VAPI_PHONE_NUMBER_ID=your_vapi_phone_number_id  # Optional
```

### **3. Database Migration**
The system will automatically create the `voice_calls` table when you start the application. The table includes:
- Call tracking information
- VAPI call IDs
- Status and timing data
- Retry scheduling
- Error logging

### **4. Assistant Configuration**
Configure your VAPI assistant with a system prompt like:

```
You are an AI assistant for an Email Monitor Agent system. When you call employees, you should:

1. Greet them professionally by name
2. Inform them about a new important email
3. Provide the sender and subject
4. Give a brief summary of the email content
5. Ask them to check their WhatsApp for full details
6. Keep the call brief and professional

Use the provided variables:
- {employeeName}: The employee's name
- {emailSender}: Who sent the email
- {emailSubject}: Email subject line
- {emailSummary}: Brief summary of the email
```

## 📋 **How It Works**

### **Email Processing Flow**
1. **Email received** → AI summarization
2. **WhatsApp notifications sent** to all active employees
3. **Voice calls initiated** simultaneously via VAPI
4. **Call status tracked** in real-time
5. **Failed calls scheduled** for retry in 10 minutes

### **Call Retry Logic**
- **Failed calls** are automatically retried up to 3 times
- **10-minute intervals** between retry attempts
- **Background service** monitors and processes retries
- **Permanent failure** status after 3 failed attempts

### **Employee Status**
- Only **active employees** receive calls
- Employee data fetched from the main API
- **Phone numbers** must include country codes (e.g., +1234567890)

## 🔧 **API Endpoints**

### **Manual Call Retry**
```http
POST /api/calls/retry
```
Manually trigger retry of failed voice calls.

### **Start Background Call Monitoring**
```http
POST /api/calls/retry/start
```
Start the background service for automatic call retries.

### **Email Processing**
```http
POST /api/emails/{email_id}/process
```
Process a specific email (includes voice calls if VAPI is enabled).

## 📊 **Call Status Types**

| Status | Description |
|--------|-------------|
| `pending` | Call created but not yet initiated |
| `initiated` | Call request sent to VAPI |
| `ringing` | Phone is ringing |
| `answered` | Call was answered |
| `completed` | Call finished successfully |
| `failed` | Call failed (will be retried) |
| `no_answer` | Call not answered (will be retried) |
| `failed_permanently` | Call failed after 3 retries |

## 🎨 **Voice Message Template**

The system generates personalized voice messages like:

```
Hello [Employee Name], this is an urgent notification from your Email Monitor Agent.

You have received a new important email from [Sender Name] with the subject: [Email Subject].

Here's a summary: [AI-generated summary]

Please check your WhatsApp for the full details and take appropriate action. Thank you.
```

## 🔍 **Monitoring & Troubleshooting**

### **Logs**
- All call activities are logged with detailed information
- Check application logs for VAPI API responses
- Error messages include specific failure reasons

### **Database Queries**
```sql
-- Check recent voice calls
SELECT * FROM voice_calls ORDER BY initiated_at DESC LIMIT 10;

-- Check failed calls needing retry
SELECT * FROM voice_calls 
WHERE status IN ('failed', 'no_answer') 
AND retry_count < 3 
AND next_retry_at <= NOW();

-- Check call success rate
SELECT status, COUNT(*) as count 
FROM voice_calls 
GROUP BY status;
```

### **Common Issues**
1. **VAPI API Key Invalid**: Check your API key in environment variables
2. **Assistant Not Found**: Verify your assistant ID
3. **Phone Number Format**: Ensure numbers include country codes
4. **No Active Employees**: Check employee status in the database

## 💰 **Cost Considerations**

- **VAPI charges** per minute of call time
- **Failed calls** may still incur minimal charges
- **Retry logic** helps minimize unnecessary costs
- **Monitor usage** through VAPI dashboard

## 🔒 **Security**

- **API keys** stored securely in environment variables
- **Phone numbers** validated before calling
- **Rate limiting** through retry delays
- **Error handling** prevents infinite loops

## 🚦 **Testing**

### **Test Mode**
Set `VAPI_ENABLED=False` to disable voice calls during testing.

### **Manual Testing**
1. Add a test employee with your phone number
2. Process a test email
3. Check if you receive the voice call
4. Verify call status in the database

## 📈 **Performance**

- **Parallel processing** of calls to multiple employees
- **Background retries** don't block main processing
- **Database indexing** on call status and retry times
- **Efficient API calls** to VAPI

## 🔄 **Integration Points**

The VAPI integration works seamlessly with:
- **WhatsApp notifications** (sent simultaneously)
- **Email processing pipeline** (automatic triggering)
- **Employee management** (active status checking)
- **AI summarization** (content for voice messages)

## 📞 **Support**

For VAPI-specific issues:
- Check [VAPI documentation](https://docs.vapi.ai)
- Contact VAPI support for API issues
- Review application logs for integration problems
