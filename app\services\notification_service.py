"""
Unified notification service that handles both WhatsApp notifications and VAPI voice calls.
"""

import logging
from typing import List, Dict, Optional
from datetime import datetime, timezone, timedelta
from ..models import EmailLog, WhatsAppNotification, VoiceCall
from .whatsapp_notifier import get_whatsapp_notifier
from .vapi_caller import get_vapi_caller

logger = logging.getLogger(__name__)


class NotificationService:
    """
    Service that handles both WhatsApp notifications and VAPI voice calls
    for email notifications to employees.
    """
    
    def __init__(self, config: Dict):
        """
        Initialize the notification service.
        
        Args:
            config: Configuration dictionary containing WhatsApp and VAPI settings
        """
        self.config = config
        self.whatsapp_notifier = get_whatsapp_notifier(config)
        self.vapi_caller = get_vapi_caller(config) if self._is_vapi_enabled() else None
        
    def _is_vapi_enabled(self) -> bool:
        """Check if VAPI is enabled in configuration."""
        return (
            self.config.get('vapi_enabled', False) and
            self.config.get('vapi_api_key') and
            self.config.get('vapi_assistant_id')
        )
    
    def send_notifications(self, email_log: EmailLog, db_session) -> Dict:
        """
        Send both WhatsApp notifications and voice calls to employees.
        
        Args:
            email_log: Processed EmailLog instance
            db_session: Database session
            
        Returns:
            Dict: Summary of notification results
        """
        results = {
            'whatsapp_notifications': [],
            'voice_calls': [],
            'success_count': 0,
            'failure_count': 0
        }
        
        try:
            # Get active employees
            active_employees = self._get_active_employees(db_session)
            
            if not active_employees:
                logger.warning("No active employees found for notifications")
                return results
            
            # Send WhatsApp notifications
            logger.info(f"Sending WhatsApp notifications for email {email_log.id}")
            whatsapp_results = self.whatsapp_notifier.send_notification(email_log, db_session)
            results['whatsapp_notifications'] = [notif.to_dict() for notif in whatsapp_results]
            
            # Send voice calls if VAPI is enabled
            if self.vapi_caller:
                logger.info(f"Initiating voice calls for email {email_log.id}")
                voice_results = self._send_voice_calls(email_log, active_employees, db_session)
                results['voice_calls'] = voice_results
            else:
                logger.info("VAPI not enabled, skipping voice calls")
            
            # Calculate success/failure counts
            results['success_count'] = len([n for n in results['whatsapp_notifications'] if n['status'] == 'sent'])
            results['success_count'] += len([c for c in results['voice_calls'] if c['status'] in ['initiated', 'ringing']])
            
            results['failure_count'] = len([n for n in results['whatsapp_notifications'] if n['status'] == 'failed'])
            results['failure_count'] += len([c for c in results['voice_calls'] if c['status'] == 'failed'])
            
            logger.info(f"Notifications sent for email {email_log.id}: {results['success_count']} successful, {results['failure_count']} failed")
            
        except Exception as e:
            logger.error(f"Error sending notifications for email {email_log.id}: {str(e)}")
            results['error'] = str(e)
        
        return results
    
    def _send_voice_calls(self, email_log: EmailLog, employees: List[Dict], db_session) -> List[Dict]:
        """
        Send voice calls to employees.
        
        Args:
            email_log: EmailLog instance
            employees: List of employee dictionaries
            db_session: Database session
            
        Returns:
            List[Dict]: List of voice call results
        """
        voice_results = []
        
        for employee in employees:
            try:
                # Create voice call record
                voice_call = VoiceCall(
                    email_log_id=email_log.id,
                    recipient=employee['phone'],
                    employee_name=employee['name'],
                    status='pending'
                )
                
                db_session.add(voice_call)
                db_session.commit()
                
                # Make the call via VAPI
                call_result = self.vapi_caller.make_call(
                    recipient_phone=employee['phone'],
                    email_log=email_log,
                    employee_name=employee['name']
                )
                
                # Update voice call record with result
                if call_result['success']:
                    voice_call.vapi_call_id = call_result.get('call_id')
                    voice_call.status = call_result.get('status', 'initiated')
                    voice_call.initiated_at = datetime.now(timezone.utc)
                    logger.info(f"Voice call initiated to {employee['name']} ({employee['phone']}): {call_result.get('call_id')}")
                else:
                    voice_call.status = 'failed'
                    voice_call.error_message = call_result.get('error', 'Unknown error')
                    voice_call.retry_count = 1
                    voice_call.next_retry_at = datetime.now(timezone.utc) + timedelta(minutes=10)
                    logger.error(f"Voice call failed to {employee['name']} ({employee['phone']}): {call_result.get('error')}")
                
                db_session.commit()
                voice_results.append(voice_call.to_dict())
                
            except Exception as e:
                logger.error(f"Exception during voice call to {employee.get('name', 'Unknown')} ({employee.get('phone', 'Unknown')}): {str(e)}")
                # Update the call record if it exists
                if 'voice_call' in locals():
                    voice_call.status = 'failed'
                    voice_call.error_message = f"Exception: {str(e)}"
                    voice_call.retry_count = 1
                    voice_call.next_retry_at = datetime.now(timezone.utc) + timedelta(minutes=10)
                    db_session.commit()
                    voice_results.append(voice_call.to_dict())
        
        return voice_results
    
    def _get_active_employees(self, db_session) -> List[Dict]:
        """
        Get active employee phone numbers and names from the API server.
        
        Args:
            db_session: Database session
            
        Returns:
            List[Dict]: List of active employees with phone and name
        """
        try:
            import requests
            
            # Get employees from the main API server
            api_base_url = self.config.get('api_base_url', 'http://localhost:8000')
            response = requests.get(f"{api_base_url}/api/employees", timeout=10)
            
            if response.status_code == 200:
                employees = response.json()
                # Filter active employees and return phone numbers with names
                active_employees = [
                    {
                        'phone': emp['phone'],
                        'name': emp['name'],
                        'id': emp['id']
                    }
                    for emp in employees 
                    if emp.get('status') == 'active'
                ]
                logger.info(f"Found {len(active_employees)} active employees")
                return active_employees
            else:
                logger.error(f"Failed to fetch employees from API: {response.status_code}")
                return []
                
        except Exception as e:
            logger.error(f"Exception fetching active employees: {str(e)}")
            return []
    
    def retry_failed_calls(self, db_session) -> Dict:
        """
        Retry failed voice calls that are scheduled for retry.
        
        Args:
            db_session: Database session
            
        Returns:
            Dict: Summary of retry results
        """
        if not self.vapi_caller:
            return {'message': 'VAPI not enabled'}
        
        try:
            # Find calls that need retry
            now = datetime.now(timezone.utc)
            failed_calls = db_session.query(VoiceCall).filter(
                VoiceCall.status.in_(['failed', 'no_answer']),
                VoiceCall.retry_count < 3,  # Max 3 retries
                VoiceCall.next_retry_at <= now
            ).all()
            
            logger.info(f"Found {len(failed_calls)} voice calls to retry")
            
            retry_results = {
                'retried_count': 0,
                'success_count': 0,
                'failure_count': 0
            }
            
            for call in failed_calls:
                try:
                    # Get the associated email log
                    email_log = db_session.query(EmailLog).filter(EmailLog.id == call.email_log_id).first()
                    if not email_log:
                        logger.error(f"Email log {call.email_log_id} not found for call {call.id}")
                        continue
                    
                    # Retry the call
                    call_result = self.vapi_caller.make_call(
                        recipient_phone=call.recipient,
                        email_log=email_log,
                        employee_name=call.employee_name
                    )
                    
                    retry_results['retried_count'] += 1
                    
                    # Update call record
                    if call_result['success']:
                        call.vapi_call_id = call_result.get('call_id')
                        call.status = call_result.get('status', 'initiated')
                        call.initiated_at = datetime.now(timezone.utc)
                        call.retry_count += 1
                        call.next_retry_at = None
                        retry_results['success_count'] += 1
                        logger.info(f"Retry successful for call {call.id}: {call_result.get('call_id')}")
                    else:
                        call.retry_count += 1
                        call.error_message = f"Retry {call.retry_count}: {call_result.get('error', 'Unknown error')}"
                        if call.retry_count < 3:
                            call.next_retry_at = datetime.now(timezone.utc) + timedelta(minutes=10)
                        else:
                            call.status = 'failed_permanently'
                            call.next_retry_at = None
                        retry_results['failure_count'] += 1
                        logger.error(f"Retry failed for call {call.id}: {call_result.get('error')}")
                    
                    db_session.commit()
                    
                except Exception as e:
                    logger.error(f"Exception during call retry {call.id}: {str(e)}")
                    call.retry_count += 1
                    call.error_message = f"Retry {call.retry_count} exception: {str(e)}"
                    if call.retry_count >= 3:
                        call.status = 'failed_permanently'
                        call.next_retry_at = None
                    else:
                        call.next_retry_at = datetime.now(timezone.utc) + timedelta(minutes=10)
                    db_session.commit()
                    retry_results['failure_count'] += 1
            
            logger.info(f"Call retry completed: {retry_results['retried_count']} retried, {retry_results['success_count']} successful, {retry_results['failure_count']} failed")
            return retry_results
            
        except Exception as e:
            logger.error(f"Exception during call retry process: {str(e)}")
            return {'error': str(e)}


def get_notification_service(config: Dict) -> NotificationService:
    """
    Factory function to get the notification service.
    
    Args:
        config: Configuration dictionary
        
    Returns:
        NotificationService: Notification service instance
    """
    return NotificationService(config)
